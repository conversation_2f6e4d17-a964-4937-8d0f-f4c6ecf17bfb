/**
 * WCAG-031: Error Suggestion Check
 * Success Criterion: 3.3.3 Error Suggestion (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface ErrorSuggestionAnalysis {
  formElements: Array<{
    element: string;
    type: string;
    selector: string;
    hasValidation: boolean;
    hasErrorMessage: boolean;
    hasSuggestion: boolean;
    errorMessageText?: string;
    suggestionText?: string;
    validationPattern?: string;
    isRequired: boolean;
    hasAriaDescribedby: boolean;
    hasAriaInvalid: boolean;
  }>;
  errorContainers: Array<{
    selector: string;
    text: string;
    isVisible: boolean;
    associatedField?: string;
    hasSuggestion: boolean;
    suggestionQuality: 'none' | 'poor' | 'good' | 'excellent';
  }>;
  totalFormElements: number;
  elementsWithValidation: number;
  elementsWithSuggestions: number;
  elementsWithPoorSuggestions: number;
}

export interface ErrorSuggestionConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class ErrorSuggestionCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Patterns that indicate good error suggestions
  private readonly goodSuggestionPatterns = [
    /please enter a valid/i,
    /format should be/i,
    /example:/i,
    /try:/i,
    /should contain/i,
    /must include/i,
    /use the format/i,
    /enter.*like/i,
    /should be.*characters/i,
    /password must/i,
  ];

  // Patterns that indicate poor error suggestions
  private readonly poorSuggestionPatterns = [
    /^invalid$/i,
    /^error$/i,
    /^wrong$/i,
    /^incorrect$/i,
    /^required$/i,
    /^field is required$/i,
    /^this field/i,
    /^please fix/i,
  ];

  async performCheck(config: ErrorSuggestionConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorSuggestionConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-031',
      'Error Suggestion',
      'understandable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeErrorSuggestionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with error suggestion analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-031',
        ruleName: 'Error Suggestion',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'error-suggestion-analysis',
          formValidationAnalysis: true,
          suggestionQualityAssessment: true,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 35,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'error-suggestion-analysis',
        confidence: 0.75,
        additionalData: {
          checkType: 'form-validation',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeErrorSuggestionCheck(
    page: Page,
    _config: ErrorSuggestionConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeErrorHandling: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze error suggestions using enhanced analyzer
    const errorSuggestionAnalysis = await this.analyzeErrorSuggestionsEnhanced(
      page,
      formAccessibilityReport,
    );

    // Analyze suggestion quality using AI semantic validation
    const suggestionQualityAnalysis = await this.analyzeSuggestionQuality(page);

    // Analyze error recovery mechanisms
    const errorRecoveryAnalysis = await this.analyzeErrorRecoveryMechanisms(page);

    // Combine analysis results
    const allAnalyses = [errorSuggestionAnalysis, suggestionQualityAnalysis, errorRecoveryAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced error suggestions analysis using FormAccessibilityAnalyzer
   */
  private async analyzeErrorSuggestionsEnhanced(page: Page, formAccessibilityReport: any) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form: any, formIndex: number) => {
      form.fields.forEach((field: any, fieldIndex: number) => {
        // Only check fields that have validation
        if (field.hasValidation) {
          totalChecks++;

          // Check if field has error suggestions
          if (field.hasAccessibleErrors && field.hasSuggestions) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Field ${fieldIndex + 1} in form ${formIndex + 1} has error suggestions`,
              value: `${field.selector} - has accessible errors and suggestions`,
              selector: field.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Field ${fieldIndex + 1} in form ${formIndex + 1} lacks error suggestions`);
            evidence.push({
              type: 'code',
              description: `Field ${fieldIndex + 1} requires error suggestions`,
              value: `${field.selector} - hasAccessibleErrors: ${field.hasAccessibleErrors}, hasSuggestions: ${field.hasSuggestions}`,
              selector: field.selector,
              severity: 'error',
            });
            recommendations.push(`Add helpful error suggestions to field ${fieldIndex + 1} in form ${formIndex + 1}`);
          }
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze suggestion quality using AI semantic validation
   */
  private async analyzeSuggestionQuality(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Find error suggestion elements
    const suggestions = await page.$$eval(
      '.error-suggestion, .help-text, .validation-message, [aria-describedby*="error"], [aria-describedby*="help"]',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.trim() || '';
          const isVisible = element.offsetParent !== null;
          const associatedField = element.getAttribute('aria-describedby') ||
                                 element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          // Analyze suggestion quality
          const isHelpful = text.length > 20 &&
                           (text.includes('should') || text.includes('must') ||
                            text.includes('format') || text.includes('example') ||
                            text.includes('try') || text.includes('correct'));

          const isSpecific = !text.toLowerCase().includes('invalid') ||
                            text.includes('format') || text.includes('example');

          return {
            index,
            text,
            isVisible,
            hasAssociatedField: !!associatedField,
            selector: `suggestion-${index}`,
            isEmpty: text.length === 0,
            isHelpful,
            isSpecific,
            quality: isHelpful && isSpecific ? 'good' : isHelpful ? 'fair' : 'poor',
          };
        });
      },
    );

    const totalChecks = suggestions.length;
    let passedChecks = 0;

    suggestions.forEach((suggestion, index) => {
      let suggestionPassed = true;

      // Check if suggestion is visible and has content
      if (!suggestion.isVisible || suggestion.isEmpty) {
        suggestionPassed = false;
        issues.push(`Error suggestion ${index + 1} is not visible or empty`);
        evidence.push({
          type: 'code',
          description: `Error suggestion ${index + 1} visibility issue`,
          value: `visible: ${suggestion.isVisible}, hasContent: ${!suggestion.isEmpty}`,
          severity: 'error',
        });
        recommendations.push(`Ensure error suggestion ${index + 1} is visible and has meaningful content`);
      }

      // Check suggestion quality
      if (suggestion.quality === 'poor') {
        suggestionPassed = false;
        issues.push(`Error suggestion ${index + 1} is not helpful`);
        evidence.push({
          type: 'code',
          description: `Error suggestion ${index + 1} needs improvement`,
          value: `suggestion: "${suggestion.text}" - quality: ${suggestion.quality}`,
          severity: 'warning',
        });
        recommendations.push(`Improve the helpfulness and specificity of error suggestion ${index + 1}`);
      }

      if (suggestionPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Error suggestion ${index + 1} is helpful and specific`,
          value: `"${suggestion.text}" - quality: ${suggestion.quality}`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze error recovery mechanisms
   */
  private async analyzeErrorRecoveryMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for error recovery features
    const recoveryFeatures = await page.$$eval('form', (forms) => {
      return forms.map((form, index) => {
        const hasUndo = form.querySelector('[type="reset"], .undo, .cancel') !== null;
        const hasAutoSave = form.hasAttribute('data-autosave') ||
                           form.querySelector('[data-autosave]') !== null;
        const hasConfirmation = form.querySelector('.confirm, .confirmation') !== null ||
                               form.hasAttribute('data-confirm');
        const hasValidationSummary = form.querySelector('.error-summary, .validation-summary') !== null;

        return {
          index,
          selector: `form:nth-of-type(${index + 1})`,
          hasUndo,
          hasAutoSave,
          hasConfirmation,
          hasValidationSummary,
          recoveryScore: [hasUndo, hasAutoSave, hasConfirmation, hasValidationSummary].filter(Boolean).length,
        };
      });
    });

    const totalChecks = recoveryFeatures.length;
    let passedChecks = 0;

    recoveryFeatures.forEach((form, index) => {
      // Forms with good recovery mechanisms (at least 2 features)
      if (form.recoveryScore >= 2) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Form ${index + 1} has good error recovery mechanisms`,
          value: `${form.selector} - recovery features: ${form.recoveryScore}/4`,
          selector: form.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Form ${index + 1} lacks adequate error recovery mechanisms`);
        evidence.push({
          type: 'code',
          description: `Form ${index + 1} needs better error recovery`,
          value: `${form.selector} - recovery features: ${form.recoveryScore}/4 (undo: ${form.hasUndo}, autosave: ${form.hasAutoSave}, confirmation: ${form.hasConfirmation}, summary: ${form.hasValidationSummary})`,
          selector: form.selector,
          severity: 'warning',
        });
        recommendations.push(`Add error recovery mechanisms to form ${index + 1} (undo, autosave, confirmation, or validation summary)`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

}

      // Analyze form elements
      formElements.forEach((element, index) => {
        const tagName = element.tagName.toLowerCase();
        const type = element.getAttribute('type') || tagName;
        const id = element.getAttribute('id');
        const name = element.getAttribute('name');
        
        // Check for validation attributes
        const hasValidation = element.hasAttribute('required') ||
                             element.hasAttribute('pattern') ||
                             element.hasAttribute('min') ||
                             element.hasAttribute('max') ||
                             element.hasAttribute('minlength') ||
                             element.hasAttribute('maxlength') ||
                             type === 'email' ||
                             type === 'url' ||
                             type === 'tel';

        const isRequired = element.hasAttribute('required') ||
                          element.getAttribute('aria-required') === 'true';

        const hasAriaDescribedby = element.hasAttribute('aria-describedby');
        const hasAriaInvalid = element.hasAttribute('aria-invalid');

        // Look for associated error messages
        let hasErrorMessage = false;
        let hasSuggestion = false;
        let errorMessageText = '';
        let suggestionText = '';

        if (hasAriaDescribedby) {
          const describedbyIds = element.getAttribute('aria-describedby')?.split(' ') || [];
          describedbyIds.forEach(describedbyId => {
            const describedElement = document.getElementById(describedbyId);
            if (describedElement) {
              const text = describedElement.textContent?.trim() || '';
              if (text) {
                hasErrorMessage = true;
                errorMessageText += text + ' ';
                
                // Check if it contains suggestions
                if (text.toLowerCase().includes('format') ||
                    text.toLowerCase().includes('example') ||
                    text.toLowerCase().includes('should') ||
                    text.toLowerCase().includes('must') ||
                    text.toLowerCase().includes('try') ||
                    text.toLowerCase().includes('use')) {
                  hasSuggestion = true;
                  suggestionText += text + ' ';
                }
              }
            }
          });
        }

        // Look for nearby error messages
        if (!hasErrorMessage) {
          const parent = element.parentElement;
          if (parent) {
            const errorElements = parent.querySelectorAll('.error, .invalid, .help-text, .field-error, [role="alert"]');
            errorElements.forEach(errorEl => {
              const text = errorEl.textContent?.trim() || '';
              if (text) {
                hasErrorMessage = true;
                errorMessageText += text + ' ';
                
                if (text.toLowerCase().includes('format') ||
                    text.toLowerCase().includes('example') ||
                    text.toLowerCase().includes('should')) {
                  hasSuggestion = true;
                  suggestionText += text + ' ';
                }
              }
            });
          }
        }

        elementAnalysis.push({
          element: tagName,
          type,
          selector: `${tagName}:nth-of-type(${index + 1})`,
          hasValidation,
          hasErrorMessage,
          hasSuggestion,
          errorMessageText: errorMessageText.trim() || undefined,
          suggestionText: suggestionText.trim() || undefined,
          validationPattern: element.getAttribute('pattern') || undefined,
          isRequired,
          hasAriaDescribedby,
          hasAriaInvalid,
        });
      });

      // Find error containers
      const errorSelectors = ['.error', '.invalid', '.field-error', '[role="alert"]', '.help-text'];
      errorSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const text = element.textContent?.trim() || '';
          if (text) {
            const computedStyle = window.getComputedStyle(element);
            const isVisible = computedStyle.display !== 'none' && 
                             computedStyle.visibility !== 'hidden';

            // Determine suggestion quality
            let suggestionQuality: 'none' | 'poor' | 'good' | 'excellent' = 'none';
            let hasSuggestion = false;

            if (text.toLowerCase().includes('format') ||
                text.toLowerCase().includes('example') ||
                text.toLowerCase().includes('should') ||
                text.toLowerCase().includes('must') ||
                text.toLowerCase().includes('try')) {
              hasSuggestion = true;
              
              if (text.length > 50 && 
                  (text.includes('example') || text.includes('format'))) {
                suggestionQuality = 'excellent';
              } else if (text.length > 20) {
                suggestionQuality = 'good';
              } else {
                suggestionQuality = 'poor';
              }
            } else if (text.toLowerCase().match(/^(invalid|error|wrong|incorrect|required)$/)) {
              suggestionQuality = 'poor';
            }

            errorContainers.push({
              selector: `${selector}:nth-of-type(${index + 1})`,
              text,
              isVisible,
              hasSuggestion,
              suggestionQuality,
            });
          }
        });
      });

      const totalFormElements = elementAnalysis.length;
      const elementsWithValidation = elementAnalysis.filter(el => el.hasValidation).length;
      const elementsWithSuggestions = elementAnalysis.filter(el => el.hasSuggestion).length;
      const elementsWithPoorSuggestions = errorContainers.filter(
        container => container.suggestionQuality === 'poor'
      ).length;

      return {
        formElements: elementAnalysis,
        errorContainers,
        totalFormElements,
        elementsWithValidation,
        elementsWithSuggestions,
        elementsWithPoorSuggestions,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = errorAnalysis.totalFormElements;

    if (totalElements === 0) {
      // No form elements found
      evidence.push({
        type: 'info',
        description: 'No form elements requiring error suggestions found',
        value: 'Page contains no form elements with validation requirements',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalFormElements: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future form validation includes helpful error suggestions'],
      };
    }

    // Analyze elements with validation but poor/missing suggestions
    const elementsNeedingSuggestions = errorAnalysis.formElements.filter(element => 
      element.hasValidation && !element.hasSuggestion
    );

    if (elementsNeedingSuggestions.length > 0) {
      const failureRate = elementsNeedingSuggestions.length / errorAnalysis.elementsWithValidation;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      elementsNeedingSuggestions.forEach((element) => {
        issues.push(`${element.element}[${element.type}]: Missing helpful error suggestions`);
        
        evidence.push({
          type: 'error',
          description: 'Form element with validation missing error suggestions',
          value: `${element.element}[type="${element.type}"] - has validation but no helpful suggestions`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: 'error',
          fixExample: {
            before: this.generateBeforeExample(element),
            after: this.generateAfterExample(element),
            description: 'Add helpful error suggestions for validation failures',
            codeExample: `
<!-- Example: Email validation with suggestion -->
<label for="email">Email Address</label>
<input type="email" id="email" required aria-describedby="email-error">
<div id="email-error" role="alert" style="display: none;">
  Please enter a valid email address. Example: <EMAIL>
</div>

<!-- Example: Password validation with detailed suggestions -->
<label for="password">Password</label>
<input type="password" id="password" required 
       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
       aria-describedby="password-help password-error">
<div id="password-help">
  Password must be at least 8 characters with uppercase, lowercase, and number
</div>
<div id="password-error" role="alert" style="display: none;">
  Password doesn't meet requirements. Try: MyPassword123
</div>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/error-suggestion.html',
              'https://webaim.org/techniques/formvalidation/',
              'https://www.w3.org/WAI/tutorials/forms/validation/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              elementType: element.type,
              hasValidation: element.hasValidation,
              hasErrorMessage: element.hasErrorMessage,
              hasSuggestion: element.hasSuggestion,
              isRequired: element.isRequired,
              validationPattern: element.validationPattern,
            },
          },
        });
      });
    }

    // Check for poor quality suggestions
    if (errorAnalysis.elementsWithPoorSuggestions > 0) {
      score = Math.max(score - (errorAnalysis.elementsWithPoorSuggestions * 10), 0);
      issues.push(`Found ${errorAnalysis.elementsWithPoorSuggestions} poor quality error suggestions`);
      
      evidence.push({
        type: 'warning',
        description: 'Poor quality error suggestions detected',
        value: `${errorAnalysis.elementsWithPoorSuggestions} error messages lack helpful suggestions`,
        selector: '.error, .invalid, [role="alert"]',
        elementCount: errorAnalysis.elementsWithPoorSuggestions,
        affectedSelectors: ['.error', '.invalid', '[role="alert"]'],
        severity: 'warning',
        fixExample: {
          before: '<div class="error">Invalid</div>',
          after: '<div class="error">Please enter a valid email address. Example: <EMAIL></div>',
          description: 'Replace generic error messages with specific, helpful suggestions',
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/error-suggestion.html'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: errorAnalysis.elementsWithPoorSuggestions,
          checkSpecificData: {
            poorSuggestionCount: errorAnalysis.elementsWithPoorSuggestions,
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Error suggestion analysis summary',
      value: `${totalElements} form elements: ${errorAnalysis.elementsWithValidation} with validation, ${errorAnalysis.elementsWithSuggestions} with suggestions`,
      selector: 'form, body',
      elementCount: totalElements,
      affectedSelectors: ['input', 'select', 'textarea'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalFormElements: errorAnalysis.totalFormElements,
          elementsWithValidation: errorAnalysis.elementsWithValidation,
          elementsWithSuggestions: errorAnalysis.elementsWithSuggestions,
          elementsWithPoorSuggestions: errorAnalysis.elementsWithPoorSuggestions,
          suggestionRate: errorAnalysis.elementsWithValidation > 0 
            ? (errorAnalysis.elementsWithSuggestions / errorAnalysis.elementsWithValidation * 100).toFixed(1)
            : '0',
        },
      },
    });

    // Generate recommendations
    if (elementsNeedingSuggestions.length > 0) {
      recommendations.push('Provide specific, helpful suggestions for all validation errors');
      recommendations.push('Include examples of correct input formats in error messages');
      recommendations.push('Use aria-describedby to associate error suggestions with form fields');
      recommendations.push('Avoid generic error messages like "Invalid" or "Error"');
    } else {
      recommendations.push('Continue providing helpful error suggestions');
      recommendations.push('Test error messages with users to ensure clarity');
    }

    if (errorAnalysis.elementsWithPoorSuggestions > 0) {
      recommendations.push('Improve error message quality with specific guidance');
      recommendations.push('Include examples and format requirements in error messages');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateBeforeExample(element: ErrorSuggestionAnalysis['formElements'][0]): string {
    if (element.type === 'email') {
      return '<input type="email" required>\n<div class="error">Invalid email</div>';
    } else if (element.type === 'password') {
      return '<input type="password" required>\n<div class="error">Invalid password</div>';
    } else if (element.type === 'tel') {
      return '<input type="tel" required>\n<div class="error">Invalid phone number</div>';
    } else {
      return `<input type="${element.type}" required>\n<div class="error">Invalid input</div>`;
    }
  }

  private generateAfterExample(element: ErrorSuggestionAnalysis['formElements'][0]): string {
    if (element.type === 'email') {
      return `<input type="email" required aria-describedby="email-error">
<div id="email-error" class="error">Please enter a valid email address. Example: <EMAIL></div>`;
    } else if (element.type === 'password') {
      return `<input type="password" required aria-describedby="password-error">
<div id="password-error" class="error">Password must be at least 8 characters with uppercase, lowercase, and number. Example: MyPassword123</div>`;
    } else if (element.type === 'tel') {
      return `<input type="tel" required aria-describedby="phone-error">
<div id="phone-error" class="error">Please enter a valid phone number. Format: (*************</div>`;
    } else {
      return `<input type="${element.type}" required aria-describedby="field-error">
<div id="field-error" class="error">Please enter a valid ${element.type}. Check the format and try again.</div>`;
    }
  }
}
