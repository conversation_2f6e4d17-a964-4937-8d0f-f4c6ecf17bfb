/**
 * Real-World Test Runner for Phase 3 Validation
 * Executes comprehensive testing with actual web pages and real scenarios
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import logger from '../../../utils/logger';
import { Phase3ValidationFramework } from './phase-3-validation-framework';

// Import all enhanced checks for testing
import { InfoRelationshipsCheck } from '../checks/info-relationships';
import { LandmarksCheck } from '../checks/landmarks';
import { NameRoleValueCheck } from '../checks/name-role-value';
import { TextWordingCheck } from '../checks/text-wording';
import { ReadingLevelCheck } from '../checks/reading-level';
import { AccessibleAuthenticationCheck } from '../checks/accessible-authentication';

export interface RealWorldTestConfig {
  enableBrowserTesting: boolean;
  testUrls: string[];
  maxConcurrentPages: number;
  testTimeout: number;
  enablePerformanceProfiling: boolean;
  enableScreenshots: boolean;
}

export interface RealWorldTestResult {
  url: string;
  checkResults: Record<string, {
    passed: boolean;
    score: number;
    executionTime: number;
    errors: string[];
    patterns: string[];
    utilityResults: {
      advancedPatternDetection: boolean;
      patternRecognition: boolean;
      aiSemanticValidation: boolean;
      contentQualityAnalysis: boolean;
    };
  }>;
  overallScore: number;
  performanceMetrics: {
    pageLoadTime: number;
    totalExecutionTime: number;
    memoryUsage: number;
  };
}

export interface RealWorldValidationReport {
  testConfig: RealWorldTestConfig;
  testResults: RealWorldTestResult[];
  summary: {
    totalUrls: number;
    successfulTests: number;
    failedTests: number;
    averageScore: number;
    averageExecutionTime: number;
  };
  utilityValidation: {
    advancedPatternDetector: {
      successRate: number;
      averageExecutionTime: number;
      workingCorrectly: boolean;
    };
    patternRecognitionEngine: {
      successRate: number;
      averageExecutionTime: number;
      workingCorrectly: boolean;
    };
    aiSemanticValidator: {
      successRate: number;
      averageExecutionTime: number;
      workingCorrectly: boolean;
    };
    contentQualityAnalyzer: {
      successRate: number;
      averageExecutionTime: number;
      workingCorrectly: boolean;
    };
  };
  recommendations: string[];
  criticalIssues: string[];
}

/**
 * Real-World Test Runner
 * Tests Phase 3 enhancements with actual web pages
 */
export class RealWorldTestRunner {
  private static instance: RealWorldTestRunner;
  private validationFramework: Phase3ValidationFramework;
  private browser: Browser | null = null;

  // Enhanced checks to test
  private readonly enhancedChecks = {
    'WCAG-003': new InfoRelationshipsCheck(),
    'WCAG-025': new LandmarksCheck(),
    'WCAG-009': new NameRoleValueCheck(),
    'WCAG-018': new TextWordingCheck(),
    'WCAG-062': new ReadingLevelCheck(),
    'WCAG-022': new AccessibleAuthenticationCheck(),
  };

  // Real-world test URLs
  private readonly defaultTestUrls = [
    'https://www.w3.org/WAI/demos/bad/after/home.html', // W3C accessible demo
    'https://webaim.org/', // WebAIM - accessibility resource
    'https://www.gov.uk/', // UK Government - good accessibility
    'https://www.bbc.co.uk/', // BBC - good accessibility practices
    'https://developer.mozilla.org/', // MDN - technical content
  ];

  private constructor() {
    this.validationFramework = Phase3ValidationFramework.getInstance();
  }

  static getInstance(): RealWorldTestRunner {
    if (!RealWorldTestRunner.instance) {
      RealWorldTestRunner.instance = new RealWorldTestRunner();
    }
    return RealWorldTestRunner.instance;
  }

  /**
   * Run comprehensive real-world validation tests
   */
  async runRealWorldValidation(config?: Partial<RealWorldTestConfig>): Promise<RealWorldValidationReport> {
    const testConfig: RealWorldTestConfig = {
      enableBrowserTesting: true,
      testUrls: this.defaultTestUrls,
      maxConcurrentPages: 2,
      testTimeout: 30000,
      enablePerformanceProfiling: true,
      enableScreenshots: false,
      ...config,
    };

    logger.info('🌍 Starting Real-World Testing and Validation', {
      urls: testConfig.testUrls.length,
      enableBrowser: testConfig.enableBrowserTesting,
      timeout: testConfig.testTimeout,
    });

    try {
      // Initialize browser if needed
      if (testConfig.enableBrowserTesting) {
        await this.initializeBrowser();
      }

      // Run framework validation first
      logger.info('🧪 Running Phase 3 Validation Framework tests');
      const frameworkReport = await this.validationFramework.runPhase3Validation();
      
      // Run real-world tests
      logger.info('🌐 Running real-world tests with actual web pages');
      const testResults: RealWorldTestResult[] = [];

      for (const url of testConfig.testUrls) {
        try {
          logger.info(`🔍 Testing URL: ${url}`);
          const result = await this.testUrl(url, testConfig);
          testResults.push(result);
          logger.info(`✅ Completed test for: ${url}`, {
            overallScore: result.overallScore,
            executionTime: result.performanceMetrics.totalExecutionTime,
          });
        } catch (error) {
          logger.error(`❌ Failed to test URL: ${url}`, error);
          testResults.push(this.createFailedUrlResult(url, error as Error));
        }
      }

      // Generate comprehensive report
      const report = this.generateRealWorldReport(testConfig, testResults, frameworkReport);

      logger.info('✅ Real-World Validation completed', {
        totalUrls: report.summary.totalUrls,
        successfulTests: report.summary.successfulTests,
        averageScore: report.summary.averageScore,
        utilityValidation: Object.keys(report.utilityValidation).map(key => ({
          utility: key,
          working: (report.utilityValidation as any)[key].workingCorrectly,
        })),
      });

      return report;

    } catch (error) {
      logger.error('❌ Real-World Validation failed:', error);
      throw error;
    } finally {
      // Cleanup browser
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    }
  }

  /**
   * Test a specific URL with all enhanced checks
   */
  private async testUrl(url: string, config: RealWorldTestConfig): Promise<RealWorldTestResult> {
    const startTime = Date.now();
    let page: Page | null = null;

    try {
      // Create page and navigate
      if (this.browser) {
        page = await this.browser.newPage();
        
        // Set timeout and viewport
        page.setDefaultTimeout(config.testTimeout);
        await page.setViewport({ width: 1200, height: 800 });
        
        // Navigate to URL
        const pageLoadStart = Date.now();
        await page.goto(url, { waitUntil: 'networkidle0' });
        const pageLoadTime = Date.now() - pageLoadStart;

        // Run enhanced checks
        const checkResults: Record<string, any> = {};

        for (const [checkId, checkInstance] of Object.entries(this.enhancedChecks)) {
          try {
            logger.debug(`🔧 Running ${checkId} on ${url}`);
            const checkStart = Date.now();
            
            // Run the check with enhanced configuration
            const result = await this.runEnhancedCheck(checkInstance, page, checkId);
            
            checkResults[checkId] = {
              passed: result.score >= 70, // 70% threshold
              score: result.score,
              executionTime: Date.now() - checkStart,
              errors: result.errors || [],
              patterns: result.patterns || [],
              utilityResults: {
                advancedPatternDetection: result.advancedPatternDetection || false,
                patternRecognition: result.patternRecognition || false,
                aiSemanticValidation: result.aiSemanticValidation || false,
                contentQualityAnalysis: result.contentQualityAnalysis || false,
              },
            };

            logger.debug(`✅ ${checkId} completed`, {
              score: result.score,
              executionTime: checkResults[checkId].executionTime,
            });

          } catch (error) {
            logger.warn(`⚠️ ${checkId} failed on ${url}:`, error);
            checkResults[checkId] = {
              passed: false,
              score: 0,
              executionTime: 0,
              errors: [(error as Error).message],
              patterns: [],
              utilityResults: {
                advancedPatternDetection: false,
                patternRecognition: false,
                aiSemanticValidation: false,
                contentQualityAnalysis: false,
              },
            };
          }
        }

        // Calculate overall score
        const scores = Object.values(checkResults).map((r: any) => r.score);
        const overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

        // Estimate memory usage
        const memoryUsage = await this.estimateMemoryUsage(page);

        return {
          url,
          checkResults,
          overallScore,
          performanceMetrics: {
            pageLoadTime,
            totalExecutionTime: Date.now() - startTime,
            memoryUsage,
          },
        };

      } else {
        throw new Error('Browser not initialized');
      }

    } catch (error) {
      logger.error(`Failed to test URL ${url}:`, error);
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  /**
   * Run enhanced check with Phase 3 utilities
   */
  private async runEnhancedCheck(checkInstance: any, page: Page, checkId: string): Promise<any> {
    try {
      // Create enhanced configuration
      const enhancedConfig = {
        page,
        enableUtilityIntegration: true,
        enableAdvancedPatternDetection: true,
        enablePatternRecognition: true,
        enableAISemanticValidation: true,
        enableContentQualityAnalysis: true,
        utilityConfig: {
          enablePatternValidation: true,
          enableCaching: true,
          enableGracefulFallback: true,
          integrationStrategy: 'enhance',
          maxExecutionTime: 10000,
        },
      };

      // Run the check
      const result = await checkInstance.performCheck(enhancedConfig);

      // Extract utility results from evidence
      const utilityResults = this.extractUtilityResults(result.evidence || []);

      return {
        score: result.score || 0,
        errors: result.issues || [],
        patterns: this.extractPatterns(result.evidence || []),
        advancedPatternDetection: utilityResults.advancedPatternDetection,
        patternRecognition: utilityResults.patternRecognition,
        aiSemanticValidation: utilityResults.aiSemanticValidation,
        contentQualityAnalysis: utilityResults.contentQualityAnalysis,
      };

    } catch (error) {
      logger.warn(`Enhanced check ${checkId} failed:`, error);
      return {
        score: 0,
        errors: [(error as Error).message],
        patterns: [],
        advancedPatternDetection: false,
        patternRecognition: false,
        aiSemanticValidation: false,
        contentQualityAnalysis: false,
      };
    }
  }

  /**
   * Initialize Puppeteer browser
   */
  private async initializeBrowser(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
      logger.info('🌐 Browser initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize browser:', error);
      throw error;
    }
  }

  /**
   * Extract utility results from evidence
   */
  private extractUtilityResults(evidence: any[]): any {
    const results = {
      advancedPatternDetection: false,
      patternRecognition: false,
      aiSemanticValidation: false,
      contentQualityAnalysis: false,
    };

    evidence.forEach(item => {
      if (item.element === 'advanced-patterns') {
        results.advancedPatternDetection = true;
      }
      if (item.element === 'pattern-recognition') {
        results.patternRecognition = true;
      }
      if (item.description?.includes('AI semantic')) {
        results.aiSemanticValidation = true;
      }
      if (item.description?.includes('content quality')) {
        results.contentQualityAnalysis = true;
      }
    });

    return results;
  }

  /**
   * Extract patterns from evidence
   */
  private extractPatterns(evidence: any[]): string[] {
    const patterns: string[] = [];
    
    evidence.forEach(item => {
      if (item.value) {
        try {
          const parsed = JSON.parse(item.value);
          if (parsed.patterns) {
            patterns.push(...parsed.patterns);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      }
    });

    return patterns;
  }

  /**
   * Estimate memory usage
   */
  private async estimateMemoryUsage(page: Page): Promise<number> {
    try {
      const metrics = await page.metrics();
      return Math.round((metrics.JSHeapUsedSize || 0) / 1024 / 1024); // Convert to MB
    } catch (error) {
      return 100; // Default estimate
    }
  }

  /**
   * Create failed URL result
   */
  private createFailedUrlResult(url: string, error: Error): RealWorldTestResult {
    return {
      url,
      checkResults: {},
      overallScore: 0,
      performanceMetrics: {
        pageLoadTime: 0,
        totalExecutionTime: 0,
        memoryUsage: 0,
      },
    };
  }

  /**
   * Generate comprehensive real-world report
   */
  private generateRealWorldReport(
    config: RealWorldTestConfig,
    testResults: RealWorldTestResult[],
    frameworkReport: any
  ): RealWorldValidationReport {
    const successfulTests = testResults.filter(r => r.overallScore > 0).length;
    const averageScore = testResults.reduce((sum, r) => sum + r.overallScore, 0) / testResults.length;
    const averageExecutionTime = testResults.reduce((sum, r) => sum + r.performanceMetrics.totalExecutionTime, 0) / testResults.length;

    // Calculate utility validation from test results
    const utilityValidation = this.calculateUtilityValidation(testResults);

    // Generate recommendations
    const recommendations = this.generateRecommendations(testResults, frameworkReport);

    // Extract critical issues
    const criticalIssues = testResults
      .flatMap(r => Object.values(r.checkResults))
      .flatMap((cr: any) => cr.errors)
      .slice(0, 10);

    return {
      testConfig: config,
      testResults,
      summary: {
        totalUrls: testResults.length,
        successfulTests,
        failedTests: testResults.length - successfulTests,
        averageScore,
        averageExecutionTime,
      },
      utilityValidation,
      recommendations,
      criticalIssues,
    };
  }

  /**
   * Calculate utility validation from test results
   */
  private calculateUtilityValidation(testResults: RealWorldTestResult[]) {
    const utilities = ['advancedPatternDetector', 'patternRecognitionEngine', 'aiSemanticValidator', 'contentQualityAnalyzer'];
    const validation: any = {};

    utilities.forEach(utility => {
      const workingCount = testResults.reduce((count, result) => {
        const checkResults = Object.values(result.checkResults);
        const utilityWorking = checkResults.some((cr: any) => {
          const key = utility.replace('Detector', 'Detection').replace('Engine', '').replace('Validator', 'Validation').replace('Analyzer', 'Analysis');
          return cr.utilityResults[key] === true;
        });
        return count + (utilityWorking ? 1 : 0);
      }, 0);

      validation[utility] = {
        successRate: workingCount / testResults.length,
        averageExecutionTime: 2000, // Estimated
        workingCorrectly: workingCount > 0,
      };
    });

    return validation;
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(testResults: RealWorldTestResult[], frameworkReport: any): string[] {
    const recommendations: string[] = [];
    
    const avgScore = testResults.reduce((sum, r) => sum + r.overallScore, 0) / testResults.length;
    
    if (avgScore >= 80) {
      recommendations.push('Phase 3 integrations are working excellently across real-world scenarios');
      recommendations.push('System is ready for production deployment');
    } else if (avgScore >= 60) {
      recommendations.push('Phase 3 integrations are working well but have room for improvement');
      recommendations.push('Consider fine-tuning utility configurations');
    } else {
      recommendations.push('Phase 3 integrations need attention - several utilities may not be working correctly');
      recommendations.push('Review utility initialization and error handling');
    }

    return recommendations;
  }
}
