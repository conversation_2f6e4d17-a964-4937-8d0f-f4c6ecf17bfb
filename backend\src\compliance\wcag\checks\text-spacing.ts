/**
 * WCAG-042: Text Spacing Check
 * Success Criterion: 1.4.12 Text Spacing (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface TextSpacingConfig extends EnhancedCheckConfig {
  enableLayoutAnalysis?: boolean;
  enableEnhancedColorAnalysis?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class TextSpacingCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();

  async performCheck(config: TextSpacingConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: TextSpacingConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableLayoutAnalysis: true,
      enableEnhancedColorAnalysis: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-042',
      'Text Spacing',
      'perceivable',
      0.0535,
      'AA',
      enhancedConfig,
      this.executeTextSpacingCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with text spacing analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-042',
        ruleName: 'Text Spacing',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'text-spacing-analysis',
          spacingMeasurement: true,
          responsiveTextValidation: true,
          layoutAnalysis: enhancedConfig.enableLayoutAnalysis,
          enhancedColorAnalysis: enhancedConfig.enableEnhancedColorAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeTextSpacingCheck(
    page: Page,
    config: TextSpacingConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced text spacing analysis using AdvancedLayoutAnalyzer
    try {
      const spacingAnalysis = await this.advancedLayoutAnalyzer.analyzeAdvancedSpacing(page);

      // Add enhanced evidence from advanced spacing analysis
      evidence.push({
        type: 'advanced-spacing-analysis',
        description: 'Advanced text spacing analysis with WCAG compliance validation',
        impact: spacingAnalysis.analysis.hasIssues ? 'critical' : 'pass',
        element: 'text-elements',
        value: JSON.stringify({
          analysis: spacingAnalysis.analysis,
          optimization: spacingAnalysis.optimization,
          thirdPartyEnhanced: spacingAnalysis.thirdPartyEnhanced,
        }),
        recommendation: spacingAnalysis.analysis.hasIssues
          ? 'Adjust text spacing to meet WCAG 1.4.12 requirements'
          : 'Text spacing meets WCAG requirements',
      });

      // Collect issues and recommendations from advanced analysis
      if (spacingAnalysis.analysis.hasIssues) {
        issues.push(...spacingAnalysis.analysis.issues);
        recommendations.push(...spacingAnalysis.optimization.suggestions);
      }

    } catch (error) {
      console.warn('Advanced spacing analysis failed, falling back to basic analysis:', error);
    }

    // Apply WCAG text spacing requirements and test - Basic fallback analysis
    const spacingAnalysis = await page.evaluate(() => {
      // WCAG 1.4.12 spacing requirements
      const spacingCSS = `
        * {
          line-height: 1.5 !important;
          letter-spacing: 0.12em !important;
          word-spacing: 0.16em !important;
        }
        p {
          margin-bottom: 2em !important;
        }
      `;

      // Create and inject test styles
      const testStyleElement = document.createElement('style');
      testStyleElement.id = 'wcag-text-spacing-test';
      testStyleElement.textContent = spacingCSS;
      document.head.appendChild(testStyleElement);

      const problematicElements: Array<{
        selector: string;
        tagName: string;
        textContent: string;
        originalHeight: number;
        newHeight: number;
        isClipped: boolean;
        hasOverflow: boolean;
        overflowType: string;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Get all text-containing elements
      const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, li, td, th, label, button, a');
      
      textElements.forEach((element, index) => {
        const textContent = element.textContent?.trim() || '';
        
        // Skip elements with no meaningful text
        if (textContent.length < 10) return;
        
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        // Skip very small elements
        if (rect.width < 50 || rect.height < 20) return;
        
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        // Check for clipping
        const isClipped = element.scrollHeight > element.clientHeight ||
                         element.scrollWidth > element.clientWidth;
        
        // Check overflow settings
        const overflow = computedStyle.overflow;
        const overflowX = computedStyle.overflowX;
        const overflowY = computedStyle.overflowY;
        
        const hasOverflow = overflow === 'hidden' || 
                           overflowX === 'hidden' || 
                           overflowY === 'hidden';
        
        if (isClipped) {
          issues.push('Content is clipped with increased text spacing');
          severity = 'error';
        }
        
        if (hasOverflow && isClipped) {
          issues.push('Overflow hidden prevents content from being visible');
          severity = 'error';
        }
        
        // Check for fixed heights that might cause issues
        const height = computedStyle.height;
        const maxHeight = computedStyle.maxHeight;
        
        if ((height.includes('px') && !height.includes('auto')) ||
            (maxHeight.includes('px') && maxHeight !== 'none')) {
          issues.push('Fixed height may prevent text spacing adaptation');
          severity = 'warning';
        }
        
        // Check for white-space settings that prevent wrapping
        const whiteSpace = computedStyle.whiteSpace;
        if (whiteSpace === 'nowrap' || whiteSpace === 'pre') {
          issues.push('White-space setting prevents text wrapping');
          severity = 'warning';
        }
        
        if (issues.length > 0) {
          problematicElements.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            textContent: textContent.substring(0, 100),
            originalHeight: rect.height,
            newHeight: element.scrollHeight,
            isClipped,
            hasOverflow,
            overflowType: overflow,
            issues,
            severity,
          });
        }
      });

      // Check for CSS that might interfere with text spacing
      const styleSheets = Array.from(document.styleSheets);
      const cssIssues: string[] = [];
      
      try {
        styleSheets.forEach(sheet => {
          if (sheet.cssRules) {
            Array.from(sheet.cssRules).forEach(rule => {
              const cssText = rule.cssText || '';
              
              // Check for !important declarations that might override spacing
              if (/line-height:\s*[^;]*!important/.test(cssText) && 
                  !/line-height:\s*1\.[5-9]/.test(cssText)) {
                cssIssues.push('CSS contains !important line-height that may prevent spacing');
              }
              
              if (/letter-spacing:\s*[^;]*!important/.test(cssText) && 
                  !/letter-spacing:\s*0\.1[2-9]em/.test(cssText)) {
                cssIssues.push('CSS contains !important letter-spacing that may prevent spacing');
              }
              
              // Check for overflow hidden on text containers
              if (/overflow:\s*hidden/.test(cssText) && 
                  /(p|h[1-6]|div|span)/.test(cssText)) {
                cssIssues.push('CSS uses overflow:hidden on text containers');
              }
            });
          }
        });
      } catch (e) {
        // Cross-origin stylesheets may not be accessible
      }

      // Remove test styles
      const testStyle = document.getElementById('wcag-text-spacing-test');
      if (testStyle) {
        testStyle.remove();
      }

      return {
        problematicElements,
        cssIssues,
        totalTextElements: textElements.length,
        problematicCount: problematicElements.length,
        clippedCount: problematicElements.filter(el => el.isClipped).length,
        overflowHiddenCount: problematicElements.filter(el => el.hasOverflow).length,
        cssIssueCount: cssIssues.length,
      };
    });

    let score = 100;
    const elementCount = spacingAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      spacingAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 15 : 
                         element.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with text spacing issues found`);
      if (spacingAnalysis.clippedCount > 0) {
        issues.push(`${spacingAnalysis.clippedCount} elements have clipped content with increased spacing`);
      }
      if (spacingAnalysis.overflowHiddenCount > 0) {
        issues.push(`${spacingAnalysis.overflowHiddenCount} elements use overflow:hidden that may clip content`);
      }

      spacingAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Text spacing issue: ${element.issues.join(', ')}`,
          value: `Text: "${element.textContent}" | Height: ${element.originalHeight}px → ${element.newHeight}px`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element.issues),
            codeExample: this.getCodeExample(element.issues[0] || 'general'),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/text-spacing.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/C36',
              'https://www.w3.org/WAI/WCAG21/Techniques/C35'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              originalHeight: element.originalHeight,
              newHeight: element.newHeight,
              isClipped: element.isClipped,
              hasOverflow: element.hasOverflow,
              overflowType: element.overflowType,
              issues: element.issues,
            },
          },
        });
      });
    }

    // CSS issues
    if (spacingAnalysis.cssIssueCount > 0) {
      score = Math.max(0, score - (spacingAnalysis.cssIssueCount * 5));
      issues.push(`${spacingAnalysis.cssIssueCount} CSS patterns may interfere with text spacing`);
    }

    // Add recommendations
    recommendations.push('Ensure content adapts to increased text spacing without loss of functionality');
    recommendations.push('Avoid fixed heights and overflow:hidden on text containers');
    recommendations.push('Use relative units and flexible layouts for text content');
    recommendations.push('Test with line-height: 1.5, letter-spacing: 0.12em, word-spacing: 0.16em');
    recommendations.push('Avoid !important declarations that prevent spacing adjustments');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.issues.includes('Fixed height')) {
      return `<${element.tagName} style="height: ${element.originalHeight}px; overflow: hidden;">Text content</${element.tagName}>`;
    }
    if (element.issues.includes('clipped')) {
      return `<${element.tagName} style="max-height: 50px; overflow: hidden;">Long text content that gets clipped</${element.tagName}>`;
    }
    return `<${element.tagName} style="overflow: hidden;">Text content</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.issues.includes('Fixed height')) {
      return `<${element.tagName} style="min-height: ${element.originalHeight}px; overflow: auto;">Text content</${element.tagName}>`;
    }
    if (element.issues.includes('clipped')) {
      return `<${element.tagName} style="overflow: auto;">Long text content that can expand</${element.tagName}>`;
    }
    return `<${element.tagName} style="overflow: visible;">Text content</${element.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('clipped')) {
      return 'Remove height restrictions and overflow:hidden to prevent clipping';
    }
    if (issues.includes('Fixed height')) {
      return 'Use min-height instead of fixed height for text containers';
    }
    if (issues.includes('White-space setting')) {
      return 'Allow text wrapping by avoiding white-space: nowrap';
    }
    return 'Ensure text containers can adapt to increased spacing';
  }

  private getCodeExample(issueType: string): string {
    switch (issueType) {
      case 'Content is clipped with increased text spacing':
        return `
/* Before: Fixed height causes clipping */
.text-container {
  height: 100px;
  overflow: hidden;
  line-height: 1.2;
}

/* After: Flexible height accommodates spacing */
.text-container {
  min-height: 100px;
  overflow: auto;
  line-height: 1.5; /* Supports up to 1.5 */
}

/* Ensure content adapts to user spacing preferences */
.text-container {
  /* Allow for increased spacing */
  letter-spacing: normal; /* Don't override user settings */
  word-spacing: normal;
  line-height: 1.5;
}
        `;
      case 'Fixed height may prevent text spacing adaptation':
        return `
/* Before: Fixed dimensions */
.card {
  height: 200px;
  width: 300px;
  overflow: hidden;
}

/* After: Flexible dimensions */
.card {
  min-height: 200px;
  max-width: 300px;
  width: 100%;
  overflow: auto;
}

/* Use flexible layouts */
.card-content {
  padding: 1rem;
  /* Avoid fixed heights for text areas */
}
        `;
      case 'White-space setting prevents text wrapping':
        return `
/* Before: Prevents wrapping */
.text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* After: Allows wrapping and spacing */
.text {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* For cases where nowrap is needed */
.text-nowrap {
  white-space: nowrap;
  /* Ensure container can scroll */
  overflow-x: auto;
}
        `;
      default:
        return `
/* General text spacing support */
.text-content {
  /* Support WCAG text spacing requirements */
  line-height: 1.5; /* Minimum for WCAG */

  /* Don't override user spacing preferences */
  letter-spacing: normal;
  word-spacing: normal;

  /* Use flexible containers */
  min-height: auto;
  overflow: visible;

  /* Avoid restrictions */
  white-space: normal;
  word-wrap: break-word;
}

/* Test with WCAG spacing requirements */
/*
line-height: 1.5 !important;
letter-spacing: 0.12em !important;
word-spacing: 0.16em !important;
paragraph spacing: 2em !important;
*/
        `;
    }
  }
}
