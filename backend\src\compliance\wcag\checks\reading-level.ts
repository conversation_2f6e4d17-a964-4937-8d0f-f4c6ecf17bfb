/**
 * WCAG-062: Reading Level Check (3.1.5 Level AAA)
 * 55% Automated - Analyzes text complexity and reading level
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ReadingLevelConfig extends EnhancedCheckConfig {
  enableAIContentAnalysis?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAISemanticValidation?: boolean;
  enableAdvancedReadabilityScoring?: boolean;
  enableComprehensiveTextAnalysis?: boolean;
}

export class ReadingLevelCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Reading level thresholds (Flesch-Kincaid Grade Level)
  private readonly GRADE_LEVELS = {
    ELEMENTARY: 6,    // 6th grade and below
    MIDDLE_SCHOOL: 8, // 7th-8th grade
    HIGH_SCHOOL: 12,  // 9th-12th grade
    COLLEGE: 16,      // College level
    GRADUATE: 20      // Graduate level
  };

  async performCheck(config: ReadingLevelConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ReadingLevelConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAIContentAnalysis: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-062',
      'Reading Level',
      'understandable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeReadingLevelCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with reading level analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-062',
        ruleName: 'Reading Level',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.55,
          checkType: 'reading-level-analysis',
          textComplexityAnalysis: true,
          readabilityScoring: true,
          aiContentAnalysis: enhancedConfig.enableAIContentAnalysis,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeReadingLevelCheck(
    page: Page,
    config: ReadingLevelConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze reading level of content
    const readingAnalysis = await page.evaluate(() => {
      const contentElements = document.querySelectorAll('p, div, article, section, main, li, td, th');
      const textBlocks: Array<{
        text: string;
        selector: string;
        element: string;
        wordCount: number;
        sentenceCount: number;
        syllableCount: number;
        averageWordsPerSentence: number;
        averageSyllablesPerWord: number;
        fleschKincaidGrade: number;
        fleschReadingEase: number;
        readingLevel: string;
        isComplex: boolean;
      }> = [];

      let totalWords = 0;
      let totalSentences = 0;
      let totalSyllables = 0;

      contentElements.forEach((element, index) => {
        const text = element.textContent || '';
        const cleanText = text.trim();
        
        // Skip very short text blocks
        if (cleanText.length < 50) return;

        const selector = generateSelector(element, index);
        
        // Calculate reading metrics
        const words = countWords(cleanText);
        const sentences = countSentences(cleanText);
        const syllables = countSyllables(cleanText);

        if (words < 10 || sentences < 1) return; // Skip insufficient content

        const avgWordsPerSentence = words / sentences;
        const avgSyllablesPerWord = syllables / words;

        // Flesch-Kincaid Grade Level formula
        const fleschKincaidGrade = 0.39 * avgWordsPerSentence + 11.8 * avgSyllablesPerWord - 15.59;
        
        // Flesch Reading Ease formula
        const fleschReadingEase = 206.835 - 1.015 * avgWordsPerSentence - 84.6 * avgSyllablesPerWord;

        // Determine reading level
        const readingLevel = getReadingLevel(fleschKincaidGrade);
        const isComplex = fleschKincaidGrade > 12; // Above high school level

        textBlocks.push({
          text: cleanText.substring(0, 200), // First 200 chars for context
          selector,
          element: element.tagName.toLowerCase(),
          wordCount: words,
          sentenceCount: sentences,
          syllableCount: syllables,
          averageWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
          averageSyllablesPerWord: Math.round(avgSyllablesPerWord * 10) / 10,
          fleschKincaidGrade: Math.round(fleschKincaidGrade * 10) / 10,
          fleschReadingEase: Math.round(fleschReadingEase * 10) / 10,
          readingLevel,
          isComplex,
        });

        totalWords += words;
        totalSentences += sentences;
        totalSyllables += syllables;
      });

      // Calculate overall metrics
      const overallAvgWordsPerSentence = totalSentences > 0 ? totalWords / totalSentences : 0;
      const overallAvgSyllablesPerWord = totalWords > 0 ? totalSyllables / totalWords : 0;
      const overallFleschKincaidGrade = totalSentences > 0 && totalWords > 0 
        ? 0.39 * overallAvgWordsPerSentence + 11.8 * overallAvgSyllablesPerWord - 15.59 
        : 0;
      const overallFleschReadingEase = totalSentences > 0 && totalWords > 0
        ? 206.835 - 1.015 * overallAvgWordsPerSentence - 84.6 * overallAvgSyllablesPerWord
        : 100;

      return {
        textBlocks,
        totalContentElements: contentElements.length,
        overallMetrics: {
          totalWords,
          totalSentences,
          totalSyllables,
          averageWordsPerSentence: Math.round(overallAvgWordsPerSentence * 10) / 10,
          averageSyllablesPerWord: Math.round(overallAvgSyllablesPerWord * 10) / 10,
          fleschKincaidGrade: Math.round(overallFleschKincaidGrade * 10) / 10,
          fleschReadingEase: Math.round(overallFleschReadingEase * 10) / 10,
          readingLevel: getReadingLevel(overallFleschKincaidGrade),
        },
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function countWords(text: string): number {
        return text.split(/\s+/).filter(word => word.length > 0).length;
      }

      function countSentences(text: string): number {
        // Count sentences by looking for sentence-ending punctuation
        const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
        return Math.max(1, sentences.length);
      }

      function countSyllables(text: string): number {
        const words = text.toLowerCase().split(/\s+/);
        let totalSyllables = 0;

        words.forEach(word => {
          // Remove punctuation
          word = word.replace(/[^a-z]/g, '');
          if (word.length === 0) return;

          // Count vowel groups
          let syllables = 0;
          let previousWasVowel = false;

          for (let i = 0; i < word.length; i++) {
            const isVowel = 'aeiouy'.includes(word[i]);
            if (isVowel && !previousWasVowel) {
              syllables++;
            }
            previousWasVowel = isVowel;
          }

          // Handle silent e
          if (word.endsWith('e') && syllables > 1) {
            syllables--;
          }

          // Every word has at least one syllable
          syllables = Math.max(1, syllables);
          totalSyllables += syllables;
        });

        return totalSyllables;
      }

      function getReadingLevel(grade: number): string {
        if (grade <= 6) return 'Elementary (6th grade or below)';
        if (grade <= 8) return 'Middle School (7th-8th grade)';
        if (grade <= 12) return 'High School (9th-12th grade)';
        if (grade <= 16) return 'College (13th-16th grade)';
        return 'Graduate (17th grade and above)';
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = readingAnalysis.textBlocks.length;

    if (elementCount > 0) {
      const complexBlocks = readingAnalysis.textBlocks.filter(block => block.isComplex);
      const overallGrade = readingAnalysis.overallMetrics.fleschKincaidGrade;

      // Score based on overall reading level
      if (overallGrade > this.GRADE_LEVELS.GRADUATE) {
        score -= 40;
        issues.push('Content requires graduate-level reading ability');
      } else if (overallGrade > this.GRADE_LEVELS.COLLEGE) {
        score -= 30;
        issues.push('Content requires college-level reading ability');
      } else if (overallGrade > this.GRADE_LEVELS.HIGH_SCHOOL) {
        score -= 20;
        issues.push('Content requires high school-level reading ability');
      } else if (overallGrade > this.GRADE_LEVELS.MIDDLE_SCHOOL) {
        score -= 10;
        issues.push('Content requires middle school-level reading ability');
      }

      // Additional penalty for individual complex blocks
      if (complexBlocks.length > 0) {
        const complexPenalty = Math.min(20, complexBlocks.length * 3);
        score -= complexPenalty;
        issues.push(`${complexBlocks.length} text blocks are above high school reading level`);
      }

      const severity = overallGrade > this.GRADE_LEVELS.COLLEGE ? 'error' : 
                      overallGrade > this.GRADE_LEVELS.HIGH_SCHOOL ? 'warning' : 'info';

      evidence.push({
        type: 'content',
        description: 'Reading level analysis',
        value: `Overall reading level: ${readingAnalysis.overallMetrics.readingLevel} (Grade ${overallGrade})`,
        elementCount,
        affectedSelectors: complexBlocks.map(block => block.selector),
        severity,
        fixExample: {
          before: 'The implementation necessitates comprehensive optimization of algorithmic methodologies.',
          after: 'The system needs better algorithms to work faster.',
          description: 'Simplify complex sentences and use common words when possible',
          codeExample: `
<!-- Before: Complex text -->
<p>The implementation necessitates comprehensive optimization of algorithmic 
methodologies to facilitate enhanced performance characteristics and ensure 
optimal user experience across diverse operational environments.</p>

<!-- After: Simplified text -->
<p>We need to improve our algorithms to make the system faster and work 
better for all users.</p>

<!-- Alternative: Provide supplementary content -->
<div class="content-with-summary">
  <div class="summary">
    <h3>Summary</h3>
    <p>We need to improve our algorithms to make the system faster.</p>
  </div>
  <div class="detailed-content">
    <h3>Technical Details</h3>
    <p>The implementation necessitates comprehensive optimization...</p>
  </div>
</div>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/reading-level.html',
            'https://www.plainlanguage.gov/',
            'https://hemingwayapp.com/'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: readingAnalysis.totalContentElements,
          checkSpecificData: {
            overallMetrics: readingAnalysis.overallMetrics,
            complexBlocks: complexBlocks.length,
            totalBlocks: elementCount,
            readingLevelDistribution: {
              elementary: readingAnalysis.textBlocks.filter(b => b.fleschKincaidGrade <= 6).length,
              middleSchool: readingAnalysis.textBlocks.filter(b => b.fleschKincaidGrade > 6 && b.fleschKincaidGrade <= 8).length,
              highSchool: readingAnalysis.textBlocks.filter(b => b.fleschKincaidGrade > 8 && b.fleschKincaidGrade <= 12).length,
              college: readingAnalysis.textBlocks.filter(b => b.fleschKincaidGrade > 12 && b.fleschKincaidGrade <= 16).length,
              graduate: readingAnalysis.textBlocks.filter(b => b.fleschKincaidGrade > 16).length,
            },
          },
        },
      });

      // Add specific examples for complex text blocks
      complexBlocks.slice(0, 5).forEach(block => {
        evidence.push({
          type: 'content',
          description: `Complex text block (Grade ${block.fleschKincaidGrade})`,
          value: `"${block.text}${block.text.length >= 200 ? '...' : ''}"`,
          selector: block.selector,
          severity: block.fleschKincaidGrade > 16 ? 'error' : 'warning',
          metadata: {
            checkSpecificData: {
              fleschKincaidGrade: block.fleschKincaidGrade,
              fleschReadingEase: block.fleschReadingEase,
              readingLevel: block.readingLevel,
              wordCount: block.wordCount,
              sentenceCount: block.sentenceCount,
              averageWordsPerSentence: block.averageWordsPerSentence,
              averageSyllablesPerWord: block.averageSyllablesPerWord,
            },
          },
        });
      });

      if (overallGrade > this.GRADE_LEVELS.HIGH_SCHOOL) {
        recommendations.push('Simplify sentence structure and use shorter sentences');
        recommendations.push('Replace complex words with simpler alternatives when possible');
        recommendations.push('Consider providing a summary or simplified version of complex content');
        recommendations.push('Use bullet points and lists to break up complex information');
        recommendations.push('Test content with actual users to ensure comprehension');
      }

      if (overallGrade > this.GRADE_LEVELS.COLLEGE) {
        recommendations.push('CRITICAL: Content may be inaccessible to many users due to complexity');
        recommendations.push('Consider providing alternative formats (audio, video, infographics)');
      }

      recommendations.push('Use plain language principles for better accessibility');
      recommendations.push('Consider your target audience\'s reading level and education background');
    } else {
      // No substantial content found
      evidence.push({
        type: 'info',
        description: 'Insufficient content for reading level analysis',
        value: 'Page does not contain enough text content for meaningful reading level analysis',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: readingAnalysis.totalContentElements,
          checkSpecificData: {
            insufficientContent: true,
            totalWords: readingAnalysis.overallMetrics.totalWords,
          },
        },
      });
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
