/**
 * WCAG Rule 4: Contrast (Minimum) - 1.4.3
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ColorAnalyzer } from '../utils/color-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import SmartCache from '../utils/smart-cache';

// Third-party color libraries for enhanced accuracy
let getContrastLib: any = null;
let ColorJSLib: any = null;

try {
  getContrastLib = require('get-contrast');
} catch (error) {
  // Fallback to built-in analysis
}

try {
  ColorJSLib = require('colorjs.io').default;
} catch (error) {
  // Fallback to built-in analysis
}
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface ContrastCheckConfig extends EnhancedCheckConfig {
  includeImages?: boolean;
  checkAllElements?: boolean;
  useEnhancedAnalysis?: boolean;
  enableGradientDetection?: boolean;
  enableCustomPropertyResolution?: boolean;
  enableThirdPartyLibraries?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableAdvancedColorSpaces?: boolean;
}

export class ContrastMinimumCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedCheckTemplate = new EnhancedCheckTemplate();
  private enhancedAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getWideGamutInstance();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform contrast minimum check - 100% automated with enhanced evidence
   */
  async performCheck(config: ContrastCheckConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ContrastCheckConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      useEnhancedAnalysis: true,
      enableGradientDetection: true,
      enableCustomPropertyResolution: true,
      enableThirdPartyLibraries: true,
      enableWideGamutAnalysis: true,
      enableAdvancedColorSpaces: true,
    };

    const result = await this.enhancedCheckTemplate.executeEnhancedCheck(
      'WCAG-004',
      'Contrast (Minimum)',
      'perceivable',
      0.1,
      'AA',
      enhancedConfig,
      this.executeContrastCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with contrast-specific analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-004',
        ruleName: 'Contrast (Minimum)',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'color-contrast-analysis',
          enhancedColorAnalysis: config.useEnhancedAnalysis !== false,
          gradientDetection: config.enableGradientDetection || false,
          customPropertyResolution: config.enableCustomPropertyResolution || false,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85, // High threshold for critical accessibility
        maxEvidenceItems: 50,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Enhanced contrast calculation using third-party libraries
   */
  private calculateEnhancedContrast(
    foreground: string,
    background: string,
    config: ContrastCheckConfig
  ): {
    ratio: number;
    score: string;
    library: string;
    accuracy: 'high' | 'medium' | 'standard';
    colorSpace?: string;
  } {
    // Try get-contrast library first (highest accuracy)
    if (config.enableThirdPartyLibraries && getContrastLib) {
      try {
        const ratio = getContrastLib.ratio(foreground, background);
        const score = getContrastLib.score(foreground, background);
        return {
          ratio,
          score,
          library: 'get-contrast',
          accuracy: 'high',
        };
      } catch (error) {
        // Fall through to next method
      }
    }

    // Try colorjs.io for advanced color spaces
    if (config.enableAdvancedColorSpaces && ColorJSLib) {
      try {
        const color1 = new ColorJSLib(foreground);
        const color2 = new ColorJSLib(background);
        const ratio = color1.contrast(color2, 'WCAG21');
        return {
          ratio,
          score: this.calculateWCAGScore(ratio),
          library: 'colorjs.io',
          accuracy: 'high',
          colorSpace: color1.space?.id || 'srgb',
        };
      } catch (error) {
        // Fall through to built-in method
      }
    }

    // Fallback to built-in ColorAnalyzer
    const contrastResult = ColorAnalyzer.analyzeContrast(foreground, background);
    return {
      ratio: contrastResult.ratio,
      score: contrastResult.level,
      library: 'built-in',
      accuracy: 'standard',
    };
  }

  /**
   * Calculate WCAG score from contrast ratio
   */
  private calculateWCAGScore(ratio: number): string {
    if (ratio >= 7.0) return 'AAA';
    if (ratio >= 4.5) return 'AA';
    if (ratio >= 3.0) return 'AA Large';
    return 'FAIL';
  }

  /**
   * Perform enhanced contrast minimum check with axe-core validation
   */
  async performEnhancedCheck(config: ContrastCheckConfig) {
    return this.enhancedCheckTemplate.executeEnhancedCheck(
      'WCAG-004',
      'Contrast Minimum',
      'perceivable',
      0.12,
      'AA',
      config,
      this.executeContrastCheck.bind(this),
      true, // Requires browser
      false, // No manual review
      {
        enableAxeValidation: config.enableAxeValidation !== false, // Default to true
        axeRules: ['color-contrast', 'color-contrast-enhanced'],
        mergeStrategy: 'supplement'
      }
    );
  }

  /**
   * Execute enhanced contrast analysis on all text elements
   */
  private async executeContrastCheck(page: Page, config: ContrastCheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if enhanced analysis is enabled (default: true)
    const useEnhanced = config.useEnhancedAnalysis !== false;

    if (useEnhanced) {
      return this.executeEnhancedContrastCheck(page, config, evidence, issues, recommendations);
    }

    // Fallback to legacy analysis
    return this.executeLegacyContrastCheck(page, config, evidence, issues, recommendations);
  }

  /**
   * Execute enhanced contrast analysis with gradient and CSS custom property support
   */
  private async executeEnhancedContrastCheck(
    page: Page,
    config: ContrastCheckConfig,
    evidence: WcagEvidence[],
    issues: string[],
    recommendations: string[],
  ) {
    try {
      // Check cache first
      const cacheKey = `enhanced-contrast:${config.targetUrl}`;
      const cached = await this.smartCache.getSiteAnalysis(config.targetUrl, 'enhanced-contrast');

      let enhancedResults;
      if (cached) {
        enhancedResults = cached;
        evidence.push({
          type: 'info',
          description: 'Enhanced contrast analysis (cached)',
          value: 'Using cached analysis results',
          severity: 'info',
        });
      } else {
        // Perform enhanced analysis
        enhancedResults = await this.enhancedAnalyzer.analyzePageContrast(page);

        // Perform wide gamut analysis if enabled
        if (config.enableWideGamutAnalysis) {
          try {
            const wideGamutResults = await this.wideGamutAnalyzer.analyzeWideGamutColors(page);

            // Add wide gamut evidence
            evidence.push({
              type: 'info',
              description: 'Wide gamut color space analysis (P3, Rec2020)',
              value: JSON.stringify({
                totalElements: wideGamutResults.totalElements,
                wideGamutElements: wideGamutResults.wideGamutElements,
                p3Coverage: wideGamutResults.p3Coverage,
                rec2020Coverage: wideGamutResults.rec2020Coverage,
                colorSpaceDistribution: wideGamutResults.colorSpaceDistribution,
              }),
              severity: wideGamutResults.wideGamutElements > 0 ? 'info' : undefined,
            });

            // Merge wide gamut results with enhanced results if applicable
            if (wideGamutResults.wideGamutElements > 0) {
              recommendations.push('Consider wide gamut color space support for enhanced color accuracy');
            }
          } catch (error) {
            console.warn('Wide gamut analysis failed:', error);
          }
        }

        // Cache results
        await this.smartCache.cacheSiteAnalysis(
          config.targetUrl,
          'enhanced-contrast',
          enhancedResults,
          3600000, // 1 hour TTL
        );
      }

      let totalElements = 0;
      let passedElements = 0;
      let gradientElements = 0;
      let imageBackgroundElements = 0;
      let customPropertyElements = 0;

      // Process enhanced results
      const resultsArray = Array.isArray(enhancedResults) ? enhancedResults : [];
      for (const result of resultsArray) {
        if (!result.text.text || result.text.text.length < 3) continue;

        totalElements++;

        // Track advanced features detected
        if (result.background.type === 'gradient') {
          gradientElements++;
        }
        if (result.background.type === 'image') {
          imageBackgroundElements++;
        }
        if (Object.keys(result.cssCustomProperties).length > 0) {
          customPropertyElements++;
        }

        const contrastRatio = result.contrast.ratio;
        const passes = result.accessibility.isAccessible;

        if (passes) {
          passedElements++;
        } else {
          issues.push(
            `Low contrast ratio (${contrastRatio}:1) for text "${result.text.text.substring(0, 50)}..." at ${result.element}`,
          );
        }

        // Add detailed evidence
        evidence.push({
          type: 'text',
          description: `Enhanced contrast analysis: ${result.text.text.substring(0, 30)}...`,
          value: `${contrastRatio}:1 (${result.accessibility.level}) - ${result.background.type} background`,
          severity: passes ? 'info' : 'error',
        });

        // Add specific recommendations for complex backgrounds
        if (result.background.type === 'gradient' && !passes) {
          recommendations.push(
            `Gradient background detected - consider using solid color overlay for better contrast`,
          );
        }
        if (result.background.type === 'image' && !passes) {
          recommendations.push(
            `Image background detected - add semi-transparent overlay to improve text readability`,
          );
        }
        if (result.contrast.confidence < 0.7) {
          recommendations.push(
            `Complex background detected - manual verification recommended for "${result.text.text.substring(0, 30)}..."`,
          );
        }
      }

      // Check library availability for enhanced accuracy reporting
      const libraryStatus = this.enhancedAnalyzer.hasEnhancedLibraries();
      const enhancedAccuracy = libraryStatus.getContrast || libraryStatus.colorJS;

      // Add enhanced analysis summary with library status
      evidence.unshift({
        type: 'info',
        description: 'Enhanced contrast analysis summary',
        value: `${passedElements}/${totalElements} elements pass. Features: ${gradientElements} gradients, ${imageBackgroundElements} images, ${customPropertyElements} custom properties. Enhanced accuracy: ${enhancedAccuracy ? 'Active' : 'Fallback mode'}`,
        severity: 'info',
      });

      // Add library status information
      if (enhancedAccuracy) {
        evidence.unshift({
          type: 'info',
          description: 'Third-party library integration status',
          value: `get-contrast: ${libraryStatus.getContrast ? 'Available' : 'Not available'}, colorjs.io: ${libraryStatus.colorJS ? 'Available' : 'Not available'}`,
          severity: 'info',
        });
      }

      // Calculate score
      const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

      // Add enhanced recommendations
      if (gradientElements > 0) {
        recommendations.push(
          'Gradient backgrounds detected - ensure sufficient contrast across all gradient stops',
        );
      }
      if (imageBackgroundElements > 0) {
        recommendations.push(
          'Image backgrounds detected - consider adding text shadows or background overlays',
        );
      }
      if (customPropertyElements > 0) {
        recommendations.push(
          'CSS custom properties detected - verify contrast in all theme variations',
        );
      }

      return {
        score,
        maxScore: 100,
        evidence,
        issues,
        recommendations,
      };
    } catch (error) {
      // Fallback to legacy analysis on error
      evidence.push({
        type: 'warning',
        description: 'Enhanced analysis failed, using legacy method',
        value: error instanceof Error ? error.message : 'Unknown error',
        severity: 'warning',
      });

      return this.executeLegacyContrastCheck(page, config, evidence, issues, recommendations);
    }
  }

  /**
   * Execute legacy contrast analysis (fallback)
   */
  private async executeLegacyContrastCheck(
    page: Page,
    config: ContrastCheckConfig,
    evidence: WcagEvidence[],
    issues: string[],
    recommendations: string[],
  ) {
    // Get all text elements with computed styles
    const textElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        text: string;
        foregroundColor: string;
        backgroundColor: string;
        fontSize: string;
        fontWeight: string;
        tagName: string;
        isVisible: boolean;
      }> = [];

      // Helper function to get effective background color
      function getEffectiveBackgroundColor(element: HTMLElement): string {
        let current = element;

        while (current && current !== document.body) {
          const style = window.getComputedStyle(current);
          const bgColor = style.backgroundColor;

          if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
            return bgColor;
          }
          current = current.parentElement as HTMLElement;
        }

        return '#ffffff'; // Default to white
      }

      // Helper function to generate selector
      function generateSelector(element: HTMLElement, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className.split(' ').filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      // Get all elements with text content
      const allElements = document.querySelectorAll('*');

      allElements.forEach((element, index) => {
        const htmlElement = element as HTMLElement;
        const computedStyle = window.getComputedStyle(htmlElement);

        // Check if element has direct text content (not just child text)
        const hasDirectText = Array.from(htmlElement.childNodes).some(
          (node) => node.nodeType === Node.TEXT_NODE && node.textContent?.trim(),
        );

        if (hasDirectText) {
          const isVisible =
            computedStyle.display !== 'none' &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.opacity !== '0';

          if (isVisible) {
            elements.push({
              selector: generateSelector(htmlElement, index),
              text: htmlElement.textContent?.trim() || '',
              foregroundColor: computedStyle.color,
              backgroundColor: getEffectiveBackgroundColor(htmlElement),
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight,
              tagName: htmlElement.tagName.toLowerCase(),
              isVisible,
            });
          }
        }
      });

      return elements;
    });

    let totalElements = 0;
    let passedElements = 0;

    // Analyze contrast for each text element
    for (const element of textElements) {
      if (element.text.length < 3) continue; // Skip very short text

      totalElements++;

      const isLargeText = ColorAnalyzer.isLargeText(element.fontSize, element.fontWeight);
      const contrastResult = ColorAnalyzer.analyzeContrast(
        element.foregroundColor,
        element.backgroundColor,
        isLargeText,
      );

      if (contrastResult.passes) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: `Text contrast passes ${contrastResult.level} standards`,
          value: `Contrast ratio: ${contrastResult.ratio}:1 (${isLargeText ? 'large' : 'normal'} text)`,
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Low contrast on ${element.selector}: ${contrastResult.ratio}:1`);

        evidence.push({
          type: 'measurement',
          description: 'Text contrast fails WCAG standards',
          value: `Contrast ratio: ${contrastResult.ratio}:1 (required: ${isLargeText ? '3.0' : '4.5'}:1)`,
          selector: element.selector,
          severity: 'error',
        });

        if (contrastResult.recommendation) {
          recommendations.push(`${element.selector}: ${contrastResult.recommendation}`);
        }
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Contrast analysis summary',
      value: `${passedElements}/${totalElements} text elements pass contrast requirements`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift('Review and improve color contrast for better accessibility');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Get effective background color by traversing parent elements
   */
  private getEffectiveBackgroundColor(element: HTMLElement): string {
    let current = element;

    while (current && current !== document.body) {
      const style = window.getComputedStyle(current);
      const bgColor = style.backgroundColor;

      if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
        return bgColor;
      }
      current = current.parentElement as HTMLElement;
    }

    return '#ffffff'; // Default to white
  }

  /**
   * Generate unique selector for element
   */
  private generateSelector(element: HTMLElement, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter((c) => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
  }
}
