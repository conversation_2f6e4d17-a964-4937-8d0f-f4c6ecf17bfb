/**
 * WCAG-047: Skip Links Check
 * Success Criterion: 2.4.1 Bypass Blocks (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface SkipLinksConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class SkipLinksCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();

  async performCheck(config: SkipLinksConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: SkipLinksConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-047',
      'Skip Links',
      'operable',
      0.0611,
      'A',
      enhancedConfig,
      this.executeSkipLinksCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with skip link analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-047',
        ruleName: 'Skip Links',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.95,
          checkType: 'skip-link-analysis',
          skipLinkDetection: true,
          bypassMechanismAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.9,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeSkipLinksCheck(
    page: Page,
    config: SkipLinksConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced skip links analysis using AdvancedLayoutAnalyzer
    try {
      const bypassAnalysis = await this.advancedLayoutAnalyzer.analyzeBypassMechanisms(page);

      // Add enhanced evidence from advanced bypass analysis
      evidence.push({
        type: 'info',
        description: 'Advanced skip links and bypass mechanism analysis',
        element: 'skip-links',
        value: JSON.stringify({
          overallScore: bypassAnalysis.overallScore,
          criticalIssues: bypassAnalysis.criticalIssues,
          recommendations: bypassAnalysis.recommendations,
          performanceMetrics: bypassAnalysis.performanceMetrics,
        }),
        severity: bypassAnalysis.criticalIssues.length > 0 ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (bypassAnalysis.criticalIssues.length > 0) {
        issues.push(...bypassAnalysis.criticalIssues);
        recommendations.push(...bypassAnalysis.recommendations);
      }

    } catch (error) {
      console.warn('Advanced skip links analysis failed, falling back to basic analysis:', error);
    }

    // Check for skip links and bypass mechanisms - Basic fallback analysis
    const skipLinksAnalysis = await page.evaluate(() => {
      const skipMechanisms: Array<{
        type: string;
        element: string;
        selector: string;
        text: string;
        href?: string;
        isVisible: boolean;
        isAccessible: boolean;
        targetExists: boolean;
        description: string;
      }> = [];

      // Check for skip links (common patterns)
      const skipLinkSelectors = [
        'a[href*="#main"]',
        'a[href*="#content"]',
        'a[href*="#skip"]',
        'a[href="#main-content"]',
        'a[href="#main-nav"]',
        'a[href="#navigation"]',
        '.skip-link',
        '.skip-to-content',
        '.skip-nav',
        '[class*="skip"]',
      ];

      skipLinkSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const link = element as HTMLAnchorElement;
          const computedStyle = window.getComputedStyle(link);
          const rect = link.getBoundingClientRect();
          
          // Check if link is visible (not hidden off-screen)
          const isVisible = rect.width > 0 && rect.height > 0 &&
                           computedStyle.visibility !== 'hidden' &&
                           computedStyle.display !== 'none';
          
          // Check if target exists
          const href = link.getAttribute('href') || '';
          const targetId = href.replace('#', '');
          const targetExists = targetId ? document.getElementById(targetId) !== null : false;
          
          // Check accessibility
          const hasText = link.textContent?.trim().length > 0;
          const hasAriaLabel = link.hasAttribute('aria-label');
          const isAccessible = hasText || hasAriaLabel;
          
          skipMechanisms.push({
            type: 'skip_link',
            element: 'a',
            selector: `${selector}:nth-of-type(${index + 1})`,
            text: link.textContent?.trim() || '',
            href,
            isVisible,
            isAccessible,
            targetExists,
            description: `Skip link: "${link.textContent?.trim()}" -> ${href}`,
          });
        });
      });

      // Check for heading structure (alternative bypass mechanism)
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const hasProperHeadingStructure = headings.length >= 2;
      
      if (hasProperHeadingStructure) {
        skipMechanisms.push({
          type: 'heading_structure',
          element: 'headings',
          selector: 'h1, h2, h3, h4, h5, h6',
          text: `${headings.length} headings found`,
          isVisible: true,
          isAccessible: true,
          targetExists: true,
          description: `Heading structure with ${headings.length} headings provides navigation`,
        });
      }

      // Check for landmark elements (alternative bypass mechanism)
      const landmarks = document.querySelectorAll(
        'main, nav, aside, header, footer, section[aria-labelledby], section[aria-label], [role="main"], [role="navigation"], [role="complementary"], [role="banner"], [role="contentinfo"]'
      );
      
      if (landmarks.length >= 2) {
        skipMechanisms.push({
          type: 'landmarks',
          element: 'landmarks',
          selector: 'main, nav, aside, header, footer, [role]',
          text: `${landmarks.length} landmarks found`,
          isVisible: true,
          isAccessible: true,
          targetExists: true,
          description: `${landmarks.length} landmark elements provide navigation structure`,
        });
      }

      // Check for table of contents
      const tocElements = document.querySelectorAll(
        '.toc, .table-of-contents, #toc, #table-of-contents, nav[aria-label*="table"], nav[aria-label*="contents"]'
      );
      
      tocElements.forEach((element, index) => {
        const links = element.querySelectorAll('a[href^="#"]');
        if (links.length > 0) {
          skipMechanisms.push({
            type: 'table_of_contents',
            element: element.tagName.toLowerCase(),
            selector: `toc:nth-of-type(${index + 1})`,
            text: `Table of contents with ${links.length} links`,
            isVisible: true,
            isAccessible: true,
            targetExists: true,
            description: `Table of contents with ${links.length} internal links`,
          });
        }
      });

      return {
        skipMechanisms,
        totalMechanisms: skipMechanisms.length,
        skipLinksCount: skipMechanisms.filter(m => m.type === 'skip_link').length,
        visibleSkipLinks: skipMechanisms.filter(m => m.type === 'skip_link' && m.isVisible).length,
        workingSkipLinks: skipMechanisms.filter(m => m.type === 'skip_link' && m.targetExists).length,
        hasAlternatives: skipMechanisms.some(m => m.type !== 'skip_link'),
      };
    });

    let score = 100;
    const elementCount = skipLinksAnalysis.totalMechanisms;
    const scanDuration = Date.now() - startTime;

    // Evaluate bypass mechanisms
    if (skipLinksAnalysis.skipLinksCount === 0 && !skipLinksAnalysis.hasAlternatives) {
      score = 0;
      issues.push('No skip links or alternative bypass mechanisms found');
      
      evidence.push({
        type: 'text',
        description: 'Missing bypass mechanisms',
        value: 'No skip links, heading structure, or landmark navigation found',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'error',
        fixExample: {
          before: '<body>\n  <div>Navigation content</div>\n  <div>Main content</div>\n</body>',
          after: '<body>\n  <a href="#main" class="skip-link">Skip to main content</a>\n  <nav>Navigation content</nav>\n  <main id="main">Main content</main>\n</body>',
          description: 'Add skip links and proper landmark structure',
          codeExample: this.getCodeExample('missing_mechanisms'),
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/bypass-blocks.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/G1',
            'https://www.w3.org/WAI/WCAG21/Techniques/G123'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            skipLinksFound: 0,
            alternativesFound: 0,
            hasHeadingStructure: false,
            hasLandmarks: false,
          },
        },
      });
    } else {
      // Check quality of existing skip links
      skipLinksAnalysis.skipMechanisms
        .filter(mechanism => mechanism.type === 'skip_link')
        .forEach((mechanism) => {
          let mechanismScore = 100;
          const mechanismIssues: string[] = [];
          
          if (!mechanism.isVisible) {
            mechanismScore -= 30;
            mechanismIssues.push('Skip link is not visible');
          }
          
          if (!mechanism.targetExists) {
            mechanismScore -= 40;
            mechanismIssues.push('Skip link target does not exist');
          }
          
          if (!mechanism.isAccessible) {
            mechanismScore -= 20;
            mechanismIssues.push('Skip link lacks accessible text');
          }
          
          if (mechanismIssues.length > 0) {
            score = Math.min(score, mechanismScore);
            issues.push(`Skip link issues: ${mechanismIssues.join(', ')}`);
            
            evidence.push({
              type: 'code',
              description: `Skip link with issues: ${mechanism.description}`,
              value: mechanism.text || mechanism.href || 'Skip link',
              selector: mechanism.selector,
              elementCount: 1,
              affectedSelectors: [mechanism.selector],
              severity: mechanismScore < 50 ? 'error' : 'warning',
              fixExample: {
                before: this.getBeforeExample(mechanism),
                after: this.getAfterExample(mechanism),
                description: this.getFixDescription(mechanismIssues),
                codeExample: this.getCodeExample('fix_skip_link'),
                resources: [
                  'https://www.w3.org/WAI/WCAG21/Understanding/bypass-blocks.html',
                  'https://webaim.org/techniques/skipnav/',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G1'
                ]
              },
              metadata: {
                scanDuration,
                elementsAnalyzed: 1,
                checkSpecificData: {
                  isVisible: mechanism.isVisible,
                  targetExists: mechanism.targetExists,
                  isAccessible: mechanism.isAccessible,
                  href: mechanism.href || '',
                },
              },
            });
          }
        });
    }

    // Add recommendations
    if (skipLinksAnalysis.skipLinksCount === 0) {
      recommendations.push('Add skip links to bypass repetitive navigation');
      recommendations.push('Implement proper heading structure for navigation');
      recommendations.push('Use landmark elements (main, nav, aside) for page structure');
    } else {
      recommendations.push('Ensure skip links are visible and functional');
      recommendations.push('Verify skip link targets exist and are properly labeled');
      recommendations.push('Test skip links with keyboard navigation');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(mechanism: any): string {
    if (!mechanism.isVisible) {
      return '<a href="#main" style="position: absolute; left: -9999px;">Skip to content</a>';
    }
    if (!mechanism.targetExists) {
      return '<a href="#nonexistent">Skip to content</a>';
    }
    if (!mechanism.isAccessible) {
      return '<a href="#main"></a>';
    }
    return '<a href="#main">Skip link with issues</a>';
  }

  private getAfterExample(mechanism: any): string {
    return '<a href="#main" class="skip-link">Skip to main content</a>\n<main id="main">Main content here</main>';
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('Skip link is not visible')) {
      return 'Make skip link visible or show on focus';
    }
    if (issues.includes('Skip link target does not exist')) {
      return 'Ensure skip link target element exists';
    }
    if (issues.includes('Skip link lacks accessible text')) {
      return 'Add descriptive text to skip link';
    }
    return 'Fix skip link implementation';
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'missing_mechanisms':
        return `
<!-- Before: No bypass mechanisms -->
<body>
  <div class="header">
    <nav>
      <ul>
        <li><a href="/home">Home</a></li>
        <li><a href="/about">About</a></li>
        <li><a href="/contact">Contact</a></li>
      </ul>
    </nav>
  </div>
  <div class="content">Main content here</div>
</body>

<!-- After: With skip links and landmarks -->
<body>
  <a href="#main" class="skip-link">Skip to main content</a>
  <a href="#nav" class="skip-link">Skip to navigation</a>

  <header>
    <nav id="nav" aria-label="Main navigation">
      <ul>
        <li><a href="/home">Home</a></li>
        <li><a href="/about">About</a></li>
        <li><a href="/contact">Contact</a></li>
      </ul>
    </nav>
  </header>

  <main id="main">
    <h1>Page Title</h1>
    <p>Main content here</p>
  </main>
</body>

<style>
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
</style>
        `;
      case 'fix_skip_link':
        return `
<!-- Before: Problematic skip link -->
<a href="#nonexistent" style="position: absolute; left: -9999px;"></a>

<!-- After: Proper skip link -->
<a href="#main" class="skip-link">Skip to main content</a>
<main id="main">
  <h1>Main Content</h1>
  <p>Content starts here</p>
</main>

<style>
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}
</style>
        `;
      default:
        return 'Implement proper skip links and bypass mechanisms';
    }
  }
}
