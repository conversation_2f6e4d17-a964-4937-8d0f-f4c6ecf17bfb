/**
 * Automation API Routes
 * REST API endpoints for the Performance Automation Controller
 */

import express from 'express';
import { PerformanceAutomationController } from '../utils/performance-automation-controller';
import { PredictivePerformanceAnalytics } from '../utils/predictive-performance-analytics';
import { AutomatedOptimizationEngine } from '../utils/automated-optimization-engine';
import logger from '../../../utils/logger';

const router = express.Router();

// Get automation controller instance
const automationController = PerformanceAutomationController.getInstance();
const predictiveAnalytics = PredictivePerformanceAnalytics.getInstance();
const optimizationEngine = AutomatedOptimizationEngine.getInstance();

/**
 * GET /api/wcag/automation/status
 * Get current system status
 */
router.get('/status', async (req, res) => {
  try {
    const status = automationController.getSystemStatus();
    const statistics = automationController.getStatistics();
    
    res.json({
      success: true,
      data: {
        status,
        statistics,
        uptime: automationController.getUptime(),
      },
    });
    
  } catch (error) {
    logger.error('Failed to get automation status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get automation status',
    });
  }
});

/**
 * GET /api/wcag/automation/config
 * Get automation configuration
 */
router.get('/config', async (req, res) => {
  try {
    const config = automationController.getConfig();
    
    res.json({
      success: true,
      data: { config },
    });
    
  } catch (error) {
    logger.error('Failed to get automation config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get automation config',
    });
  }
});

/**
 * PUT /api/wcag/automation/config
 * Update automation configuration
 */
router.put('/config', async (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({
        success: false,
        error: 'Configuration object required',
      });
    }
    
    automationController.updateConfig(config);
    
    res.json({
      success: true,
      message: 'Configuration updated successfully',
      data: { config: automationController.getConfig() },
    });
    
  } catch (error) {
    logger.error('Failed to update automation config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update automation config',
    });
  }
});

/**
 * POST /api/wcag/automation/start
 * Start automation system
 */
router.post('/start', async (req, res) => {
  try {
    await automationController.start();
    
    res.json({
      success: true,
      message: 'Automation system started successfully',
    });
    
  } catch (error) {
    logger.error('Failed to start automation system:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start automation system',
    });
  }
});

/**
 * POST /api/wcag/automation/stop
 * Stop automation system
 */
router.post('/stop', async (req, res) => {
  try {
    await automationController.stop();
    
    res.json({
      success: true,
      message: 'Automation system stopped successfully',
    });
    
  } catch (error) {
    logger.error('Failed to stop automation system:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stop automation system',
    });
  }
});

/**
 * POST /api/wcag/automation/enable-full
 * Enable full automation
 */
router.post('/enable-full', async (req, res) => {
  try {
    automationController.enableFullAutomation();
    
    res.json({
      success: true,
      message: 'Full automation enabled successfully',
    });
    
  } catch (error) {
    logger.error('Failed to enable full automation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to enable full automation',
    });
  }
});

/**
 * POST /api/wcag/automation/disable
 * Disable automation (monitoring only)
 */
router.post('/disable', async (req, res) => {
  try {
    automationController.disableAutomation();
    
    res.json({
      success: true,
      message: 'Automation disabled - monitoring only mode',
    });
    
  } catch (error) {
    logger.error('Failed to disable automation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to disable automation',
    });
  }
});

/**
 * GET /api/wcag/automation/predictions
 * Get recent predictions
 */
router.get('/predictions', async (req, res) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    const predictions = predictiveAnalytics.getPredictions(hours);
    
    res.json({
      success: true,
      data: { predictions },
    });
    
  } catch (error) {
    logger.error('Failed to get predictions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get predictions',
    });
  }
});

/**
 * GET /api/wcag/automation/recommendations
 * Get optimization recommendations
 */
router.get('/recommendations', async (req, res) => {
  try {
    const priority = req.query.priority as string;
    const recommendations = predictiveAnalytics.getRecommendations(priority);
    
    res.json({
      success: true,
      data: { recommendations },
    });
    
  } catch (error) {
    logger.error('Failed to get recommendations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get recommendations',
    });
  }
});

/**
 * GET /api/wcag/automation/optimizations
 * Get optimization history
 */
router.get('/optimizations', async (req, res) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    const optimizations = optimizationEngine.getOptimizationHistory(hours);
    
    res.json({
      success: true,
      data: { optimizations },
    });
    
  } catch (error) {
    logger.error('Failed to get optimizations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get optimizations',
    });
  }
});

/**
 * GET /api/wcag/automation/optimizations/active
 * Get active optimizations
 */
router.get('/optimizations/active', async (req, res) => {
  try {
    const activeOptimizations = optimizationEngine.getActiveOptimizations();
    
    res.json({
      success: true,
      data: { activeOptimizations },
    });
    
  } catch (error) {
    logger.error('Failed to get active optimizations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get active optimizations',
    });
  }
});

/**
 * POST /api/wcag/automation/optimizations/:id/approve
 * Approve pending optimization
 */
router.post('/optimizations/:id/approve', async (req, res) => {
  try {
    const { id } = req.params;
    const { approvedBy } = req.body;
    
    if (!approvedBy) {
      return res.status(400).json({
        success: false,
        error: 'approvedBy field required',
      });
    }
    
    const success = optimizationEngine.approveOptimization(id, approvedBy);
    
    if (success) {
      res.json({
        success: true,
        message: 'Optimization approved and executed',
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Optimization not found or not pending approval',
      });
    }
    
  } catch (error) {
    logger.error('Failed to approve optimization:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to approve optimization',
    });
  }
});

/**
 * GET /api/wcag/automation/models
 * Get predictive models information
 */
router.get('/models', async (req, res) => {
  try {
    const models = predictiveAnalytics.getModels();
    
    res.json({
      success: true,
      data: { models },
    });
    
  } catch (error) {
    logger.error('Failed to get models:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get models',
    });
  }
});

/**
 * GET /api/wcag/automation/health
 * Get system health check
 */
router.get('/health', async (req, res) => {
  try {
    const status = automationController.getSystemStatus();
    const uptime = automationController.getUptime();
    const statistics = automationController.getStatistics();
    
    const health = {
      status: status?.overall || 'unknown',
      uptime: Math.floor(uptime / 1000),
      components: status?.components || {},
      performance: status?.performance || {},
      automation: status?.automation || {},
      timestamp: new Date(),
    };
    
    res.json({
      success: true,
      data: { health },
    });
    
  } catch (error) {
    logger.error('Failed to get health status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get health status',
    });
  }
});

/**
 * GET /api/wcag/automation/metrics
 * Get current performance metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const status = automationController.getSystemStatus();
    
    if (!status) {
      return res.status(404).json({
        success: false,
        error: 'No metrics available',
      });
    }
    
    res.json({
      success: true,
      data: {
        performance: status.performance,
        components: status.components,
        automation: status.automation,
        timestamp: status.timestamp,
      },
    });
    
  } catch (error) {
    logger.error('Failed to get metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get metrics',
    });
  }
});

/**
 * POST /api/wcag/automation/test
 * Test automation system (development endpoint)
 */
router.post('/test', async (req, res) => {
  try {
    const { testType } = req.body;
    
    switch (testType) {
      case 'prediction':
        // Trigger a test prediction
        logger.info('🧪 Triggering test prediction');
        res.json({
          success: true,
          message: 'Test prediction triggered',
        });
        break;
        
      case 'optimization':
        // Trigger a test optimization
        logger.info('🧪 Triggering test optimization');
        res.json({
          success: true,
          message: 'Test optimization triggered',
        });
        break;
        
      case 'alert':
        // Trigger a test alert
        logger.info('🧪 Triggering test alert');
        res.json({
          success: true,
          message: 'Test alert triggered',
        });
        break;
        
      default:
        res.status(400).json({
          success: false,
          error: 'Invalid test type. Use: prediction, optimization, or alert',
        });
    }
    
  } catch (error) {
    logger.error('Failed to run test:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run test',
    });
  }
});

export default router;
