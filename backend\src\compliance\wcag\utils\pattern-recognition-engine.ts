/**
 * Pattern Recognition Engine
 * Advanced pattern recognition system for WCAG compliance analysis
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import { AdvancedPatternDetector, AdvancedPatternAnalysisReport } from './advanced-pattern-detector';
import { AccessibilityPatternLibrary } from './accessibility-pattern-library';

export interface PatternRecognitionConfig {
  enableAdvancedDetection: boolean;
  enableRealTimeAnalysis: boolean;
  enablePatternLearning: boolean;
  enableCrossPageAnalysis: boolean;
  patternCacheSize: number;
  analysisTimeout: number;
}

export interface RecognizedPattern {
  id: string;
  name: string;
  type: 'accessibility' | 'usability' | 'semantic' | 'behavioral';
  confidence: number;
  elements: PatternElement[];
  wcagCriteria: string[];
  compliance: 'pass' | 'fail' | 'warning' | 'needs-review';
  recommendations: string[];
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface PatternElement {
  selector: string;
  role: string;
  attributes: Record<string, string>;
  textContent: string;
  position: { x: number; y: number };
  isVisible: boolean;
  isAccessible: boolean;
}

export interface PatternRecognitionReport {
  totalPatterns: number;
  recognizedPatterns: RecognizedPattern[];
  patternCategories: Record<string, number>;
  complianceBreakdown: Record<string, number>;
  criticalIssues: string[];
  recommendations: string[];
  overallScore: number;
  analysisMetrics: {
    processingTime: number;
    patternsPerSecond: number;
    accuracyScore: number;
    coverageScore: number;
  };
}

export interface PatternLearningData {
  pattern: RecognizedPattern;
  context: string;
  userFeedback?: 'correct' | 'incorrect' | 'partial';
  timestamp: Date;
  pageUrl: string;
}

/**
 * Advanced Pattern Recognition Engine
 */
export class PatternRecognitionEngine {
  private static instance: PatternRecognitionEngine;
  private advancedDetector: AdvancedPatternDetector;
  private patternLibrary: AccessibilityPatternLibrary;
  private patternCache: Map<string, RecognizedPattern[]> = new Map();
  private learningData: PatternLearningData[] = [];
  private config: PatternRecognitionConfig;

  private constructor() {
    this.advancedDetector = AdvancedPatternDetector.getInstance();
    this.patternLibrary = AccessibilityPatternLibrary.getInstance();
    this.config = {
      enableAdvancedDetection: true,
      enableRealTimeAnalysis: true,
      enablePatternLearning: true,
      enableCrossPageAnalysis: false,
      patternCacheSize: 100,
      analysisTimeout: 10000,
    };
  }

  static getInstance(): PatternRecognitionEngine {
    if (!PatternRecognitionEngine.instance) {
      PatternRecognitionEngine.instance = new PatternRecognitionEngine();
    }
    return PatternRecognitionEngine.instance;
  }

  /**
   * Perform comprehensive pattern recognition analysis
   */
  async recognizePatterns(page: Page, config?: Partial<PatternRecognitionConfig>): Promise<PatternRecognitionReport> {
    const analysisConfig = { ...this.config, ...config };
    const startTime = Date.now();

    logger.info('🎯 Starting pattern recognition analysis');

    try {
      // Get page URL for caching
      const pageUrl = await page.url();
      const cacheKey = this.generateCacheKey(pageUrl);

      // Check cache first
      if (this.patternCache.has(cacheKey)) {
        logger.debug('📋 Using cached pattern recognition results');
        return this.generateReportFromCache(cacheKey, startTime);
      }

      // Perform advanced pattern detection
      const advancedReport = analysisConfig.enableAdvancedDetection
        ? await this.advancedDetector.performAdvancedPatternDetection(page)
        : await this.patternLibrary.analyzePatterns(page);

      // Recognize and classify patterns
      const recognizedPatterns = await this.classifyAndRecognizePatterns(page, advancedReport);

      // Apply pattern learning if enabled
      if (analysisConfig.enablePatternLearning) {
        await this.applyPatternLearning(recognizedPatterns, pageUrl);
      }

      // Generate comprehensive report
      const report = this.generatePatternRecognitionReport(recognizedPatterns, startTime);

      // Cache results
      this.cachePatternResults(cacheKey, recognizedPatterns);

      logger.info('✅ Pattern recognition completed', {
        totalPatterns: report.totalPatterns,
        processingTime: report.analysisMetrics.processingTime,
        overallScore: report.overallScore,
      });

      return report;

    } catch (error) {
      logger.error('❌ Pattern recognition failed:', error);
      throw error;
    }
  }

  /**
   * Classify and recognize patterns from detection results
   */
  private async classifyAndRecognizePatterns(
    page: Page, 
    detectionReport: AdvancedPatternAnalysisReport
  ): Promise<RecognizedPattern[]> {
    const recognizedPatterns: RecognizedPattern[] = [];

    // Process base patterns
    for (const result of detectionReport.results) {
      const pattern = this.convertToRecognizedPattern(result, 'accessibility');
      if (pattern) {
        recognizedPatterns.push(pattern);
      }
    }

    // Process semantic patterns
    if ('semanticPatterns' in detectionReport) {
      for (const semanticMatch of detectionReport.semanticPatterns) {
        const pattern = this.convertSemanticToRecognizedPattern(semanticMatch);
        if (pattern) {
          recognizedPatterns.push(pattern);
        }
      }
    }

    // Process behavioral patterns
    if ('behavioralPatterns' in detectionReport) {
      for (const behavioralPattern of detectionReport.behavioralPatterns) {
        const pattern = this.convertBehavioralToRecognizedPattern(behavioralPattern);
        if (pattern) {
          recognizedPatterns.push(pattern);
        }
      }
    }

    // Enhance patterns with additional analysis
    await this.enhancePatternsWithContextualAnalysis(page, recognizedPatterns);

    return recognizedPatterns;
  }

  /**
   * Apply machine learning and pattern learning
   */
  private async applyPatternLearning(patterns: RecognizedPattern[], pageUrl: string): Promise<void> {
    logger.debug('🧠 Applying pattern learning');

    for (const pattern of patterns) {
      // Store learning data
      const learningData: PatternLearningData = {
        pattern,
        context: this.extractPatternContext(pattern),
        timestamp: new Date(),
        pageUrl,
      };

      this.learningData.push(learningData);

      // Apply learned improvements
      await this.applyLearnedImprovements(pattern);
    }

    // Cleanup old learning data
    this.cleanupLearningData();
  }

  /**
   * Enhance patterns with contextual analysis
   */
  private async enhancePatternsWithContextualAnalysis(page: Page, patterns: RecognizedPattern[]): Promise<void> {
    logger.debug('🔍 Enhancing patterns with contextual analysis');

    try {
      // Inject contextual analysis functions
      await page.evaluate(() => {
        (window as any).analyzePatternContext = (selector: string) => {
          const element = document.querySelector(selector);
          if (!element) return null;

          return {
            parentContext: element.parentElement?.tagName || 'unknown',
            siblingCount: element.parentElement?.children.length || 0,
            documentPosition: element.getBoundingClientRect(),
            semanticContext: element.closest('[role], main, nav, header, footer, aside')?.tagName || 'none',
            interactionContext: {
              hasClickHandler: !!(element as any).onclick,
              hasKeyboardHandler: !!(element as any).onkeydown,
              isFocusable: element.tabIndex >= 0 || ['a', 'button', 'input', 'select', 'textarea'].includes(element.tagName.toLowerCase()),
            },
          };
        };
      });

      // Enhance each pattern with contextual data
      for (const pattern of patterns) {
        for (const element of pattern.elements) {
          try {
            const context = await page.evaluate((selector) => {
              return (window as any).analyzePatternContext(selector);
            }, element.selector);

            if (context) {
              // Update pattern confidence based on context
              pattern.confidence = this.adjustConfidenceBasedOnContext(pattern.confidence, context);
              
              // Add contextual recommendations
              const contextualRecommendations = this.generateContextualRecommendations(context);
              pattern.recommendations.push(...contextualRecommendations);
            }
          } catch (error) {
            logger.warn(`Failed to analyze context for ${element.selector}:`, error);
          }
        }
      }

    } catch (error) {
      logger.warn('Contextual analysis failed:', error);
    }
  }

  /**
   * Generate pattern recognition report
   */
  private generatePatternRecognitionReport(patterns: RecognizedPattern[], startTime: number): PatternRecognitionReport {
    const processingTime = Date.now() - startTime;
    
    // Calculate category breakdown
    const patternCategories: Record<string, number> = {};
    const complianceBreakdown: Record<string, number> = {};
    
    patterns.forEach(pattern => {
      patternCategories[pattern.type] = (patternCategories[pattern.type] || 0) + 1;
      complianceBreakdown[pattern.compliance] = (complianceBreakdown[pattern.compliance] || 0) + 1;
    });

    // Extract critical issues
    const criticalIssues = patterns
      .filter(p => p.impact === 'critical' && p.compliance === 'fail')
      .map(p => `Critical ${p.type} pattern issue: ${p.name}`);

    // Aggregate recommendations
    const recommendations = Array.from(new Set(
      patterns.flatMap(p => p.recommendations)
    )).slice(0, 10);

    // Calculate overall score
    const overallScore = this.calculateOverallPatternScore(patterns);

    return {
      totalPatterns: patterns.length,
      recognizedPatterns: patterns,
      patternCategories,
      complianceBreakdown,
      criticalIssues,
      recommendations,
      overallScore,
      analysisMetrics: {
        processingTime,
        patternsPerSecond: patterns.length / (processingTime / 1000),
        accuracyScore: this.calculateAccuracyScore(patterns),
        coverageScore: this.calculateCoverageScore(patterns),
      },
    };
  }

  // Helper methods
  private convertToRecognizedPattern(result: any, type: string): RecognizedPattern | null {
    // Implementation for converting detection results to recognized patterns
    return null;
  }

  private convertSemanticToRecognizedPattern(semanticMatch: any): RecognizedPattern | null {
    // Implementation for converting semantic matches to recognized patterns
    return null;
  }

  private convertBehavioralToRecognizedPattern(behavioralPattern: any): RecognizedPattern | null {
    // Implementation for converting behavioral patterns to recognized patterns
    return null;
  }

  private extractPatternContext(pattern: RecognizedPattern): string {
    return `${pattern.type}:${pattern.name}:${pattern.confidence}`;
  }

  private async applyLearnedImprovements(pattern: RecognizedPattern): Promise<void> {
    // Implementation for applying learned improvements
  }

  private cleanupLearningData(): void {
    // Keep only recent learning data (last 1000 entries)
    if (this.learningData.length > 1000) {
      this.learningData = this.learningData.slice(-1000);
    }
  }

  private adjustConfidenceBasedOnContext(confidence: number, context: any): number {
    // Adjust confidence based on contextual factors
    let adjustedConfidence = confidence;
    
    if (context.interactionContext?.isFocusable) {
      adjustedConfidence += 0.1;
    }
    
    if (context.semanticContext !== 'none') {
      adjustedConfidence += 0.05;
    }
    
    return Math.min(adjustedConfidence, 1.0);
  }

  private generateContextualRecommendations(context: any): string[] {
    const recommendations: string[] = [];
    
    if (!context.interactionContext?.isFocusable && context.interactionContext?.hasClickHandler) {
      recommendations.push('Consider making clickable element keyboard accessible');
    }
    
    if (context.semanticContext === 'none') {
      recommendations.push('Consider adding semantic context with appropriate ARIA roles');
    }
    
    return recommendations;
  }

  private calculateOverallPatternScore(patterns: RecognizedPattern[]): number {
    if (patterns.length === 0) return 0;
    
    const totalScore = patterns.reduce((sum, pattern) => {
      let score = pattern.confidence;
      if (pattern.compliance === 'pass') score += 0.2;
      if (pattern.compliance === 'fail') score -= 0.3;
      return sum + score;
    }, 0);
    
    return Math.max(0, Math.min(100, (totalScore / patterns.length) * 100));
  }

  private calculateAccuracyScore(patterns: RecognizedPattern[]): number {
    // Calculate accuracy based on confidence levels
    const avgConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length;
    return avgConfidence * 100;
  }

  private calculateCoverageScore(patterns: RecognizedPattern[]): number {
    // Calculate coverage based on pattern types detected
    const uniqueTypes = new Set(patterns.map(p => p.type));
    const maxTypes = 4; // accessibility, usability, semantic, behavioral
    return (uniqueTypes.size / maxTypes) * 100;
  }

  private generateCacheKey(url: string): string {
    return `pattern_${url.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  private generateReportFromCache(cacheKey: string, startTime: number): PatternRecognitionReport {
    const patterns = this.patternCache.get(cacheKey) || [];
    return this.generatePatternRecognitionReport(patterns, startTime);
  }

  private cachePatternResults(cacheKey: string, patterns: RecognizedPattern[]): void {
    // Implement LRU cache logic
    if (this.patternCache.size >= this.config.patternCacheSize) {
      const firstKey = this.patternCache.keys().next().value;
      this.patternCache.delete(firstKey);
    }
    this.patternCache.set(cacheKey, patterns);
  }
}
