/**
 * WCAG Integration Tests Execution Script
 * Milestone 4.3: Integration Testing and Validation
 * 
 * This script runs comprehensive integration tests for all enhanced WCAG checks
 * to validate utility integration, performance, memory usage, and concurrency.
 */

import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import logger from '../../../utils/logger';
import WCAGIntegrationTestRunner from './integration-test-runner';
import { ValidationReport } from './integration-test-framework';

interface TestExecutionConfig {
  headless: boolean;
  testUrl: string;
  enableScreenshots: boolean;
  screenshotPath: string;
  timeout: number;
  retries: number;
}

/**
 * Main test execution function
 */
async function runIntegrationTests(): Promise<void> {
  const config: TestExecutionConfig = {
    headless: true,
    testUrl: 'https://www.w3.org/WAI/WCAG21/working-examples/',
    enableScreenshots: false,
    screenshotPath: './screenshots',
    timeout: 30000,
    retries: 2,
  };

  let browser: Browser | null = null;
  let page: Page | null = null;

  try {
    logger.info('🚀 Starting WCAG Integration Tests - Milestone 4.3');
    logger.info(`📋 Test Configuration:
      - Test URL: ${config.testUrl}
      - Headless: ${config.headless}
      - Timeout: ${config.timeout}ms
      - Retries: ${config.retries}
    `);

    // Initialize browser and page
    browser = await chromium.launch({ 
      headless: config.headless,
      timeout: config.timeout,
    });
    
    page = await browser.newPage();
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Navigate to test page
    logger.info(`🌐 Navigating to test page: ${config.testUrl}`);
    await page.goto(config.testUrl, { 
      waitUntil: 'networkidle',
      timeout: config.timeout,
    });

    // Initialize test runner
    const testRunner = new WCAGIntegrationTestRunner({
      enableDetailedLogging: true,
      generateReport: true,
      exportResults: true,
      testSuites: ['enhanced-checks'],
      maxConcurrentTests: 3,
    });

    // Run comprehensive integration tests
    logger.info('🧪 Executing comprehensive integration tests...');
    const report = await testRunner.runAllTests(page);

    // Display results summary
    displayTestSummary(report);

    // Run specific category tests if needed
    await runCategorySpecificTests(testRunner, page);

    logger.info('✅ All integration tests completed successfully!');

  } catch (error) {
    logger.error(`❌ Integration tests failed: ${error}`);
    
    if (page && config.enableScreenshots) {
      try {
        const screenshotPath = `${config.screenshotPath}/error-${Date.now()}.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        logger.info(`📸 Error screenshot saved: ${screenshotPath}`);
      } catch (screenshotError) {
        logger.warn(`⚠️ Failed to save error screenshot: ${screenshotError}`);
      }
    }
    
    process.exit(1);
  } finally {
    // Cleanup
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * Display test summary results
 */
function displayTestSummary(report: ValidationReport): void {
  logger.info(`
📊 WCAG Integration Test Results Summary
========================================

Overall Results:
  ✅ Passed Checks: ${report.passedChecks}/${report.totalChecks}
  ❌ Failed Checks: ${report.failedChecks}
  📈 Overall Score: ${report.summary.overallScore.toFixed(1)}%

Category Scores:
  🔗 Compatibility: ${report.summary.compatibilityScore.toFixed(1)}%
  ⚡ Performance: ${report.summary.performanceScore.toFixed(1)}%
  🧠 Memory: ${report.summary.memoryScore.toFixed(1)}%
  🔄 Concurrency: ${report.summary.concurrencyScore.toFixed(1)}%

Performance Metrics:
  ⏱️ Avg Execution Time: ${report.overallMetrics.executionTime.toFixed(0)}ms
  💾 Avg Memory Usage: ${report.overallMetrics.memoryUsage.toFixed(1)}MB
  📋 Cache Hit Rate: ${report.overallMetrics.cacheHitRate.toFixed(1)}%
  🔧 Utility Success Rate: ${report.overallMetrics.utilitySuccessRate.toFixed(1)}%

Issues:
  🚨 Total Errors: ${report.overallMetrics.errorCount}
  ⚠️ Total Warnings: ${report.overallMetrics.warningCount}

Report Generated: ${report.timestamp}
  `);

  // Display category-specific results
  Object.entries(report.categoryResults).forEach(([category, results]) => {
    if (results.length > 0) {
      const passed = results.filter(r => r.passed).length;
      const total = results.length;
      const status = passed === total ? '✅' : '⚠️';
      
      logger.info(`${status} ${category.toUpperCase()}: ${passed}/${total} tests passed`);
      
      // Show failed tests
      const failed = results.filter(r => !r.passed);
      if (failed.length > 0) {
        failed.forEach(result => {
          logger.warn(`   ❌ ${result.checkId}: ${result.errors.join(', ')}`);
        });
      }
    }
  });
}

/**
 * Run category-specific tests for detailed analysis
 */
async function runCategorySpecificTests(testRunner: WCAGIntegrationTestRunner, page: Page): Promise<void> {
  logger.info('🎯 Running category-specific tests for detailed analysis...');

  const categories = [
    { name: 'Color/Contrast', checkIds: ['WCAG-004', 'WCAG-012'] },
    { name: 'Focus Management', checkIds: ['WCAG-007', 'WCAG-010', 'WCAG-011', 'WCAG-012'] },
    { name: 'Layout Analysis', checkIds: ['WCAG-014', 'WCAG-037'] },
  ];

  for (const category of categories) {
    try {
      logger.info(`📋 Testing ${category.name} category...`);
      const results = await testRunner.runSpecificTests(category.checkIds, page);
      
      const passed = results.filter(r => r.passed).length;
      const total = results.length;
      const status = passed === total ? '✅' : '⚠️';
      
      logger.info(`${status} ${category.name}: ${passed}/${total} tests passed`);
      
    } catch (error) {
      logger.error(`❌ Category test failed for ${category.name}: ${error}`);
    }
  }
}

/**
 * Validate test environment
 */
async function validateTestEnvironment(): Promise<boolean> {
  try {
    logger.info('🔍 Validating test environment...');
    
    // Check if required utilities are available
    const requiredUtilities = [
      '../utils/advanced-layout-analyzer',
      '../utils/advanced-focus-tracker',
      '../utils/wide-gamut-color-analyzer',
      '../utils/enhanced-color-analyzer',
      '../utils/smart-cache',
    ];

    for (const utility of requiredUtilities) {
      try {
        await import(utility);
        logger.debug(`✅ Utility available: ${utility}`);
      } catch (error) {
        logger.error(`❌ Missing utility: ${utility}`);
        return false;
      }
    }

    // Check if enhanced checks are available
    const requiredChecks = [
      '../checks/contrast-minimum',
      '../checks/focus-visible',
      '../checks/focus-not-obscured-minimum',
      '../checks/focus-not-obscured-enhanced',
      '../checks/focus-appearance',
      '../checks/target-size',
      '../checks/resize-text',
    ];

    for (const check of requiredChecks) {
      try {
        await import(check);
        logger.debug(`✅ Check available: ${check}`);
      } catch (error) {
        logger.error(`❌ Missing check: ${check}`);
        return false;
      }
    }

    logger.info('✅ Test environment validation passed');
    return true;

  } catch (error) {
    logger.error(`❌ Test environment validation failed: ${error}`);
    return false;
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    // Validate environment first
    const isValid = await validateTestEnvironment();
    if (!isValid) {
      logger.error('❌ Test environment validation failed. Please ensure all utilities and checks are properly implemented.');
      process.exit(1);
    }

    // Run integration tests
    await runIntegrationTests();
    
  } catch (error) {
    logger.error(`❌ Test execution failed: ${error}`);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    logger.error(`❌ Unhandled error: ${error}`);
    process.exit(1);
  });
}

export { runIntegrationTests, validateTestEnvironment, displayTestSummary };
export default main;
