/**
 * Dashboard WebSocket Service
 * Provides real-time updates for the WCAG monitoring dashboard via WebSocket connections
 */

import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { RealTimeMonitoringDashboard, DashboardMetrics } from './real-time-monitoring-dashboard';
import logger from '../../../utils/logger';

export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'metrics' | 'alert' | 'scan_update' | 'config_update' | 'error';
  data?: any;
  timestamp: Date;
  clientId?: string;
}

export interface DashboardClient {
  id: string;
  ws: WebSocket;
  subscriptions: Set<string>;
  lastPing: Date;
  isAlive: boolean;
}

/**
 * Dashboard WebSocket Service Class
 */
export class DashboardWebSocketService {
  private static instance: DashboardWebSocketService;
  
  private wss: WebSocketServer | null = null;
  private clients: Map<string, DashboardClient> = new Map();
  private dashboard: RealTimeMonitoringDashboard;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;
  
  private constructor() {
    this.dashboard = RealTimeMonitoringDashboard.getInstance();
    this.setupDashboardListeners();
    logger.info('🔌 Dashboard WebSocket Service initialized');
  }
  
  static getInstance(): DashboardWebSocketService {
    if (!DashboardWebSocketService.instance) {
      DashboardWebSocketService.instance = new DashboardWebSocketService();
    }
    return DashboardWebSocketService.instance;
  }
  
  /**
   * Start WebSocket server
   */
  start(port: number = 8081): void {
    if (this.isRunning) {
      logger.warn('🔌 WebSocket service already running');
      return;
    }
    
    this.wss = new WebSocketServer({ 
      port,
      perMessageDeflate: false,
    });
    
    this.wss.on('connection', (ws: WebSocket, request: IncomingMessage) => {
      this.handleConnection(ws, request);
    });
    
    this.wss.on('error', (error) => {
      logger.error('🔌 WebSocket server error:', error);
    });
    
    // Start heartbeat
    this.startHeartbeat();
    
    this.isRunning = true;
    logger.info(`🔌 Dashboard WebSocket Service started on port ${port}`);
  }
  
  /**
   * Stop WebSocket server
   */
  stop(): void {
    if (!this.isRunning) return;
    
    // Close all client connections
    this.clients.forEach(client => {
      client.ws.close();
    });
    this.clients.clear();
    
    // Stop heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Close server
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    
    this.isRunning = false;
    logger.info('🔌 Dashboard WebSocket Service stopped');
  }
  
  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: WebSocket, request: IncomingMessage): void {
    const clientId = this.generateClientId();
    const client: DashboardClient = {
      id: clientId,
      ws,
      subscriptions: new Set(),
      lastPing: new Date(),
      isAlive: true,
    };
    
    this.clients.set(clientId, client);
    
    // Setup client event handlers
    ws.on('message', (data: Buffer) => {
      this.handleMessage(client, data);
    });
    
    ws.on('close', () => {
      this.handleDisconnection(client);
    });
    
    ws.on('error', (error) => {
      logger.error(`🔌 Client ${clientId} error:`, error);
      this.handleDisconnection(client);
    });
    
    ws.on('pong', () => {
      client.isAlive = true;
      client.lastPing = new Date();
    });
    
    // Send welcome message
    this.sendMessage(client, {
      type: 'config_update',
      data: {
        clientId,
        dashboardConfig: this.dashboard.getConfig(),
        currentMetrics: this.dashboard.getCurrentMetrics(),
      },
      timestamp: new Date(),
    });
    
    logger.info(`🔌 Client connected: ${clientId} (total: ${this.clients.size})`);
  }
  
  /**
   * Handle client disconnection
   */
  private handleDisconnection(client: DashboardClient): void {
    this.clients.delete(client.id);
    logger.info(`🔌 Client disconnected: ${client.id} (total: ${this.clients.size})`);
  }
  
  /**
   * Handle incoming message from client
   */
  private handleMessage(client: DashboardClient, data: Buffer): void {
    try {
      const message: WebSocketMessage = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(client, message.data);
          break;
          
        case 'unsubscribe':
          this.handleUnsubscription(client, message.data);
          break;
          
        default:
          logger.warn(`🔌 Unknown message type from client ${client.id}:`, message.type);
      }
      
    } catch (error) {
      logger.error(`🔌 Failed to parse message from client ${client.id}:`, error);
      this.sendMessage(client, {
        type: 'error',
        data: { message: 'Invalid message format' },
        timestamp: new Date(),
      });
    }
  }
  
  /**
   * Handle subscription request
   */
  private handleSubscription(client: DashboardClient, subscriptionData: any): void {
    const { channels } = subscriptionData || {};
    
    if (Array.isArray(channels)) {
      channels.forEach(channel => {
        client.subscriptions.add(channel);
      });
      
      logger.debug(`🔌 Client ${client.id} subscribed to:`, channels);
      
      // Send current metrics if subscribed to metrics
      if (client.subscriptions.has('metrics')) {
        const currentMetrics = this.dashboard.getCurrentMetrics();
        if (currentMetrics) {
          this.sendMessage(client, {
            type: 'metrics',
            data: currentMetrics,
            timestamp: new Date(),
          });
        }
      }
    }
  }
  
  /**
   * Handle unsubscription request
   */
  private handleUnsubscription(client: DashboardClient, subscriptionData: any): void {
    const { channels } = subscriptionData || {};
    
    if (Array.isArray(channels)) {
      channels.forEach(channel => {
        client.subscriptions.delete(channel);
      });
      
      logger.debug(`🔌 Client ${client.id} unsubscribed from:`, channels);
    }
  }
  
  /**
   * Send message to client
   */
  private sendMessage(client: DashboardClient, message: WebSocketMessage): void {
    if (client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error(`🔌 Failed to send message to client ${client.id}:`, error);
      }
    }
  }
  
  /**
   * Broadcast message to all subscribed clients
   */
  private broadcast(channel: string, message: WebSocketMessage): void {
    this.clients.forEach(client => {
      if (client.subscriptions.has(channel)) {
        this.sendMessage(client, message);
      }
    });
  }
  
  /**
   * Setup dashboard event listeners
   */
  private setupDashboardListeners(): void {
    this.dashboard.on('metricsUpdated', (metrics: DashboardMetrics) => {
      this.broadcast('metrics', {
        type: 'metrics',
        data: metrics,
        timestamp: new Date(),
      });
    });
    
    this.dashboard.on('scanStarted', (data) => {
      this.broadcast('scan_updates', {
        type: 'scan_update',
        data: { type: 'started', ...data },
        timestamp: new Date(),
      });
    });
    
    this.dashboard.on('scanCompleted', (data) => {
      this.broadcast('scan_updates', {
        type: 'scan_update',
        data: { type: 'completed', ...data },
        timestamp: new Date(),
      });
    });
    
    this.dashboard.on('configUpdated', (config) => {
      this.broadcast('config', {
        type: 'config_update',
        data: config,
        timestamp: new Date(),
      });
    });
    
    this.dashboard.on('metricsError', (error) => {
      this.broadcast('alerts', {
        type: 'alert',
        data: {
          type: 'system',
          severity: 'high',
          message: 'Dashboard metrics collection error',
          error: error instanceof Error ? error.message : String(error),
        },
        timestamp: new Date(),
      });
    });
  }
  
  /**
   * Start heartbeat to check client connections
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach(client => {
        if (!client.isAlive) {
          logger.debug(`🔌 Terminating inactive client: ${client.id}`);
          client.ws.terminate();
          this.clients.delete(client.id);
          return;
        }
        
        client.isAlive = false;
        client.ws.ping();
      });
    }, 30000); // 30 seconds
  }
  
  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.clients.size;
  }
  
  /**
   * Get client subscriptions summary
   */
  getSubscriptionsSummary(): Record<string, number> {
    const summary: Record<string, number> = {};
    
    this.clients.forEach(client => {
      client.subscriptions.forEach(channel => {
        summary[channel] = (summary[channel] || 0) + 1;
      });
    });
    
    return summary;
  }
  
  /**
   * Send alert to all subscribed clients
   */
  sendAlert(alert: {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    scanId?: string;
    ruleId?: string;
  }): void {
    this.broadcast('alerts', {
      type: 'alert',
      data: alert,
      timestamp: new Date(),
    });
  }
}

export default DashboardWebSocketService;
