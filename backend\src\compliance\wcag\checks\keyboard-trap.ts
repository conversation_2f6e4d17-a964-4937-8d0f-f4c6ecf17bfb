/**
 * WCAG-027: No Keyboard Trap Check
 * Success Criterion: 2.1.2 No Keyboard Trap (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FocusTracker } from '../utils/focus-tracker';
import { AdvancedFocusTracker } from '../utils/advanced-focus-tracker';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface KeyboardTrapAnalysis {
  interactiveElements: Array<{
    tagName: string;
    selector: string;
    hasTabIndex: boolean;
    tabIndex: number;
    isVisible: boolean;
    hasFocusHandler: boolean;
    hasKeydownHandler: boolean;
    hasKeyupHandler: boolean;
    hasEscapeHandler: boolean;
    role?: string;
    ariaLabel?: string;
  }>;
  potentialTraps: Array<{
    element: string;
    selector: string;
    reason: string;
    severity: 'warning' | 'error';
    hasEscapeRoute: boolean;
    hasInstructions: boolean;
  }>;
  focusableElementCount: number;
  customFocusHandlers: number;
  modalElements: number;
}

interface AdvancedTrapDetectionResult {
  trapDetected: boolean;
  trapType: 'modal' | 'dialog' | 'widget' | 'custom' | 'none';
  escapeRoutes: string[];
  trapSeverity: 'low' | 'medium' | 'high' | 'critical';
  affectedElements: string[];
  detectionConfidence: number;
  recommendations: string[];
}

interface EscapeMechanismAnalysis {
  hasEscapeKey: boolean;
  hasCloseButton: boolean;
  hasClickOutside: boolean;
  hasTabEscape: boolean;
  hasAriaEscape: boolean;
  escapeInstructions: string[];
  mechanismCount: number;
}

export interface KeyboardTrapConfig extends EnhancedCheckConfig {
  enableAdvancedTrapDetection?: boolean;
  enableEscapeMechanismValidation?: boolean;
  enableModalTrapTesting?: boolean;
  enableComplexWidgetAnalysis?: boolean;
  enableAdvancedFocusTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class KeyboardTrapCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private focusTracker = new FocusTracker();
  private advancedFocusTracker = AdvancedFocusTracker.getAdvancedInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();

  async performCheck(config: KeyboardTrapConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized trap detection
    const enhancedConfig: KeyboardTrapConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 3000, // Target: <3s performance
      },
      enableAdvancedTrapDetection: true,
      enableEscapeMechanismValidation: true,
      enableModalTrapTesting: true,
      enableComplexWidgetAnalysis: true,
      enableAdvancedFocusTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-027',
      'No Keyboard Trap',
      'operable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeKeyboardTrapCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with keyboard trap analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-027',
        ruleName: 'No Keyboard Trap',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'keyboard-trap-analysis',
          focusManagementValidation: true,
          advancedFocusTracking: enhancedConfig.enableAdvancedFocusTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          modalTrapDetection: true,
          escapeRouteValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'keyboard-trap-detection',
        confidence: 0.85,
        additionalData: {
          checkType: 'keyboard-navigation',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeKeyboardTrapCheck(
    page: Page,
    _config: KeyboardTrapConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Trap Detection Algorithm - Advanced Implementation
    const advancedTrapDetection = await this.executeAdvancedTrapDetection(page);

    // Escape Mechanism Validation Algorithm
    const escapeMechanismAnalysis = await this.analyzeEscapeMechanisms(page);

    // Modal and Dialog Trap Testing Algorithm
    const modalTrapAnalysis = await this.analyzeModalTraps(page);

    // Complex Widget Trap Analysis Algorithm
    const widgetTrapAnalysis = await this.analyzeComplexWidgetTraps(page);

    // Combine all specialized detection results
    const allAnalyses = [
      advancedTrapDetection,
      escapeMechanismAnalysis,
      modalTrapAnalysis,
      widgetTrapAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 92% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Advanced Trap Detection Algorithm - Core Implementation
   * Target: 92% keyboard trap detection accuracy
   */
  private async executeAdvancedTrapDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Advanced trap detection using focus flow analysis
    const trapDetectionResult = await page.evaluate((): AdvancedTrapDetectionResult => {
      const focusableElements = Array.from(document.querySelectorAll(
        'input, button, select, textarea, a[href], [tabindex]:not([tabindex="-1"]), [role="button"], [role="link"], [role="tab"], [role="menuitem"]'
      ));

      let trapDetected = false;
      let trapType: AdvancedTrapDetectionResult['trapType'] = 'none';
      const escapeRoutes: string[] = [];
      const affectedElements: string[] = [];
      let detectionConfidence = 0;

      // Test focus flow for trap detection
      for (let i = 0; i < focusableElements.length; i++) {
        const element = focusableElements[i] as HTMLElement;
        element.focus();

        // Simulate Tab key navigation
        const tabEvent = new KeyboardEvent('keydown', { key: 'Tab', bubbles: true });
        element.dispatchEvent(tabEvent);

        // Check if focus moved as expected
        const nextElement = document.activeElement;
        const expectedNext = focusableElements[i + 1] || focusableElements[0];

        // Detect potential trap patterns
        if (nextElement === element && focusableElements.length > 1) {
          trapDetected = true;
          trapType = 'custom';
          affectedElements.push(`${element.tagName.toLowerCase()}:nth-of-type(${i + 1})`);
          detectionConfidence += 0.3;
        }

        // Check for modal/dialog traps
        const modal = element.closest('[role="dialog"], [role="alertdialog"], .modal, .dialog');
        if (modal && nextElement && !modal.contains(nextElement)) {
          trapDetected = true;
          trapType = 'modal';
          affectedElements.push(modal.tagName.toLowerCase());
          detectionConfidence += 0.4;
        }

        // Check for widget traps
        const widget = element.closest('[role="tabpanel"], [role="menu"], [role="listbox"], [role="tree"]');
        if (widget && nextElement && !widget.contains(nextElement) && widget.contains(element)) {
          trapDetected = true;
          trapType = 'widget';
          affectedElements.push(widget.tagName.toLowerCase());
          detectionConfidence += 0.3;
        }
      }

      // Check for escape routes
      const escapeElements = document.querySelectorAll('[data-dismiss], .close, .cancel, [aria-label*="close"]');
      escapeElements.forEach((el) => {
        escapeRoutes.push(el.tagName.toLowerCase());
      });

      // Determine trap severity
      let trapSeverity: AdvancedTrapDetectionResult['trapSeverity'] = 'low';
      if (trapDetected) {
        if (escapeRoutes.length === 0) trapSeverity = 'critical';
        else if (escapeRoutes.length === 1) trapSeverity = 'high';
        else if (escapeRoutes.length === 2) trapSeverity = 'medium';
      }

      return {
        trapDetected,
        trapType,
        escapeRoutes,
        trapSeverity,
        affectedElements,
        detectionConfidence: Math.min(detectionConfidence, 1.0),
        recommendations: trapDetected ? [
          'Add escape mechanisms (Esc key, close button)',
          'Ensure focus can move freely between elements',
          'Provide clear instructions for keyboard navigation',
        ] : [],
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (!trapDetectionResult.trapDetected) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Advanced trap detection: No keyboard traps detected',
        value: `Detection confidence: ${(trapDetectionResult.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Keyboard trap detected: ${trapDetectionResult.trapType} trap`);
      evidence.push({
        type: 'code',
        description: `Advanced trap detection: ${trapDetectionResult.trapType} trap detected`,
        value: `Severity: ${trapDetectionResult.trapSeverity}, Affected: ${trapDetectionResult.affectedElements.join(', ')}, Escape routes: ${trapDetectionResult.escapeRoutes.length}`,
        severity: trapDetectionResult.trapSeverity === 'critical' ? 'error' : 'warning',
      });
      recommendations.push(...trapDetectionResult.recommendations);
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Escape Mechanism Validation Algorithm
   */
  private async analyzeEscapeMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const escapeMechanisms = await page.evaluate((): EscapeMechanismAnalysis => {
      const hasEscapeKey = document.addEventListener ? true : false; // Simplified check
      const hasCloseButton = document.querySelectorAll('.close, [data-dismiss], [aria-label*="close"]').length > 0;
      const hasClickOutside = document.querySelectorAll('[data-backdrop="true"], .modal-backdrop').length > 0;
      const hasTabEscape = document.querySelectorAll('[role="dialog"] [tabindex="0"]:last-child').length > 0;
      const hasAriaEscape = document.querySelectorAll('[aria-describedby*="escape"], [aria-label*="escape"]').length > 0;

      const escapeInstructions = Array.from(document.querySelectorAll('*'))
        .map(el => el.textContent || '')
        .filter(text => text.toLowerCase().includes('escape') || text.toLowerCase().includes('close'))
        .slice(0, 3);

      const mechanismCount = [hasEscapeKey, hasCloseButton, hasClickOutside, hasTabEscape, hasAriaEscape]
        .filter(Boolean).length;

      return {
        hasEscapeKey,
        hasCloseButton,
        hasClickOutside,
        hasTabEscape,
        hasAriaEscape,
        escapeInstructions,
        mechanismCount,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (escapeMechanisms.mechanismCount >= 2) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Escape mechanisms validation: Sufficient escape routes available',
        value: `${escapeMechanisms.mechanismCount}/5 escape mechanisms detected`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient escape mechanisms for keyboard traps');
      evidence.push({
        type: 'code',
        description: 'Escape mechanisms validation: Insufficient escape routes',
        value: `Only ${escapeMechanisms.mechanismCount}/5 escape mechanisms found`,
        severity: 'warning',
      });
      recommendations.push('Add multiple escape mechanisms (Esc key, close button, click outside)');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Modal and Dialog Trap Testing Algorithm
   */
  private async analyzeModalTraps(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const modalAnalysis = await page.$$eval(
      '[role="dialog"], [role="alertdialog"], .modal, .dialog, .popup',
      (modals) => {
        return modals.map((modal, index) => {
          const focusableInModal = modal.querySelectorAll(
            'input, button, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
          );

          const hasCloseButton = modal.querySelector('.close, [data-dismiss], [aria-label*="close"]') !== null;
          const hasEscapeHandler = modal.hasAttribute('data-keyboard') ||
                                  modal.getAttribute('data-keyboard') !== 'false';
          const hasAriaLabel = modal.hasAttribute('aria-label') || modal.hasAttribute('aria-labelledby');
          const hasFocusManagement = modal.querySelector('[autofocus]') !== null ||
                                   modal.hasAttribute('tabindex');

          return {
            index,
            selector: `modal-${index}`,
            focusableCount: focusableInModal.length,
            hasCloseButton,
            hasEscapeHandler,
            hasAriaLabel,
            hasFocusManagement,
            isProperlyImplemented: hasCloseButton && hasEscapeHandler && hasAriaLabel && hasFocusManagement,
          };
        });
      }
    );

    const totalChecks = modalAnalysis.length;
    let passedChecks = 0;

    modalAnalysis.forEach((modal, index) => {
      if (modal.isProperlyImplemented) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Modal ${index + 1} has proper trap prevention`,
          value: `${modal.selector} - close button: ${modal.hasCloseButton}, escape handler: ${modal.hasEscapeHandler}, focus management: ${modal.hasFocusManagement}`,
          severity: 'info',
        });
      } else {
        issues.push(`Modal ${index + 1} may create keyboard trap`);
        evidence.push({
          type: 'code',
          description: `Modal ${index + 1} needs trap prevention improvements`,
          value: `${modal.selector} - missing: ${!modal.hasCloseButton ? 'close button ' : ''}${!modal.hasEscapeHandler ? 'escape handler ' : ''}${!modal.hasFocusManagement ? 'focus management' : ''}`,
          severity: 'warning',
        });
        recommendations.push(`Improve modal ${index + 1} trap prevention mechanisms`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Complex Widget Trap Analysis Algorithm
   */
  private async analyzeComplexWidgetTraps(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const widgetAnalysis = await page.$$eval(
      '[role="tabpanel"], [role="menu"], [role="listbox"], [role="tree"], [role="grid"], [role="combobox"]',
      (widgets) => {
        return widgets.map((widget, index) => {
          const role = widget.getAttribute('role');
          const hasArrowKeySupport = widget.hasAttribute('data-arrow-keys') ||
                                   widget.querySelector('[data-arrow-keys]') !== null;
          const hasEscapeSupport = widget.hasAttribute('data-escape') ||
                                 widget.querySelector('[data-escape]') !== null;
          const hasTabSupport = widget.hasAttribute('data-tab-navigation') ||
                               widget.querySelectorAll('[tabindex="0"]').length > 0;
          const hasInstructions = widget.querySelector('.sr-only, .visually-hidden') !== null ||
                                widget.hasAttribute('aria-describedby');

          const complexityScore = [hasArrowKeySupport, hasEscapeSupport, hasTabSupport, hasInstructions]
            .filter(Boolean).length;

          return {
            index,
            role,
            selector: `${role}-widget-${index}`,
            hasArrowKeySupport,
            hasEscapeSupport,
            hasTabSupport,
            hasInstructions,
            complexityScore,
            isAccessible: complexityScore >= 3,
          };
        });
      }
    );

    const totalChecks = widgetAnalysis.length;
    let passedChecks = 0;

    widgetAnalysis.forEach((widget, index) => {
      if (widget.isAccessible) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Complex widget ${index + 1} (${widget.role}) has proper keyboard navigation`,
          value: `${widget.selector} - accessibility score: ${widget.complexityScore}/4`,
          severity: 'info',
        });
      } else {
        issues.push(`Complex widget ${index + 1} may create navigation traps`);
        evidence.push({
          type: 'code',
          description: `Complex widget ${index + 1} (${widget.role}) needs keyboard navigation improvements`,
          value: `${widget.selector} - accessibility score: ${widget.complexityScore}/4, missing: ${!widget.hasArrowKeySupport ? 'arrow keys ' : ''}${!widget.hasEscapeSupport ? 'escape ' : ''}${!widget.hasTabSupport ? 'tab navigation ' : ''}${!widget.hasInstructions ? 'instructions' : ''}`,
          severity: 'warning',
        });
        recommendations.push(`Add proper keyboard navigation to ${widget.role} widget ${index + 1}`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

}

    // Analyze keyboard trap potential - Basic fallback analysis
    const trapAnalysis = await page.evaluate((): KeyboardTrapAnalysis => {
      const interactiveSelectors = [
        'a[href]',
        'button',
        'input:not([type="hidden"])',
        'select',
        'textarea',
        '[tabindex]:not([tabindex="-1"])',
        '[role="button"]',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="option"]',
        '[contenteditable="true"]'
      ];

      const interactiveElements = document.querySelectorAll(interactiveSelectors.join(', '));
      const elementAnalysis: KeyboardTrapAnalysis['interactiveElements'] = [];
      const potentialTraps: KeyboardTrapAnalysis['potentialTraps'] = [];

      interactiveElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const isVisible = computedStyle.display !== 'none' && 
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';

        const tabIndex = element.getAttribute('tabindex');
        const hasTabIndex = tabIndex !== null;
        const tabIndexValue = hasTabIndex ? parseInt(tabIndex, 10) : 0;

        // Check for event handlers that might create traps
        const hasFocusHandler = element.hasAttribute('onfocus') || 
                               (element as any)._listeners?.focus?.length > 0;
        const hasKeydownHandler = element.hasAttribute('onkeydown') || 
                                 (element as any)._listeners?.keydown?.length > 0;
        const hasKeyupHandler = element.hasAttribute('onkeyup') || 
                               (element as any)._listeners?.keyup?.length > 0;

        // Check for escape key handling
        const hasEscapeHandler = element.hasAttribute('onkeydown') && 
                                element.getAttribute('onkeydown')?.includes('Escape');

        const analysis = {
          tagName: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasTabIndex,
          tabIndex: tabIndexValue,
          isVisible,
          hasFocusHandler,
          hasKeydownHandler,
          hasKeyupHandler,
          hasEscapeHandler,
          role: element.getAttribute('role') || undefined,
          ariaLabel: element.getAttribute('aria-label') || undefined,
        };

        elementAnalysis.push(analysis);

        // Detect potential keyboard traps
        if (isVisible && (hasKeydownHandler || hasKeyupHandler || hasFocusHandler)) {
          // Check for modal-like behavior
          const isModal = element.getAttribute('role') === 'dialog' ||
                         element.getAttribute('aria-modal') === 'true' ||
                         element.classList.contains('modal') ||
                         element.classList.contains('dialog');

          // Check for custom focus management
          const hasCustomFocus = hasKeydownHandler && 
                                (element.getAttribute('onkeydown')?.includes('focus') ||
                                 element.getAttribute('onkeydown')?.includes('Tab'));

          if (isModal || hasCustomFocus) {
            const hasEscapeRoute = hasEscapeHandler || 
                                  element.querySelector('[aria-label*="close"]') !== null ||
                                  element.querySelector('.close') !== null ||
                                  element.querySelector('[data-dismiss]') !== null;

            const hasInstructions = element.querySelector('[aria-describedby]') !== null ||
                                   element.getAttribute('aria-describedby') !== null ||
                                   element.textContent?.toLowerCase().includes('press escape') ||
                                   element.textContent?.toLowerCase().includes('close');

            potentialTraps.push({
              element: element.tagName.toLowerCase(),
              selector: analysis.selector,
              reason: isModal ? 'Modal dialog without proper escape mechanism' : 
                     'Custom focus management detected',
              severity: (!hasEscapeRoute && !hasInstructions) ? 'error' : 'warning',
              hasEscapeRoute,
              hasInstructions,
            });
          }
        }
      });

      // Count different types of elements
      const focusableElementCount = elementAnalysis.filter(el => el.isVisible).length;
      const customFocusHandlers = elementAnalysis.filter(el => 
        el.hasFocusHandler || el.hasKeydownHandler || el.hasKeyupHandler
      ).length;
      const modalElements = document.querySelectorAll('[role="dialog"], [aria-modal="true"], .modal').length;

      return {
        interactiveElements: elementAnalysis,
        potentialTraps,
        focusableElementCount,
        customFocusHandlers,
        modalElements,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = trapAnalysis.focusableElementCount;

    // Analyze potential keyboard traps
    if (trapAnalysis.potentialTraps.length > 0) {
      const errorTraps = trapAnalysis.potentialTraps.filter(trap => trap.severity === 'error');
      const warningTraps = trapAnalysis.potentialTraps.filter(trap => trap.severity === 'warning');

      if (errorTraps.length > 0) {
        score = 0; // Critical failure for confirmed traps
        
        errorTraps.forEach((trap, index) => {
          issues.push(`Keyboard trap detected: ${trap.reason}`);
          
          evidence.push({
            type: 'error',
            description: 'Potential keyboard trap detected',
            value: `Element: ${trap.element} - ${trap.reason}`,
            selector: trap.selector,
            elementCount: 1,
            affectedSelectors: [trap.selector],
            severity: 'error',
            fixExample: {
              before: `<div role="dialog">Content without escape mechanism</div>`,
              after: `<div role="dialog" aria-modal="true">
  <button aria-label="Close dialog" onclick="closeDialog()">×</button>
  Content with escape mechanism
</div>`,
              description: 'Provide escape mechanism for modal dialogs',
              codeExample: `
// Add keyboard event handler for escape key
element.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
});

// Or provide visible close button
<button aria-label="Close dialog" onclick="closeDialog()">
  Close
</button>
              `,
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/no-keyboard-trap.html',
                'https://www.w3.org/WAI/ARIA/apg/patterns/dialogmodal/',
                'https://developer.mozilla.org/en-US/docs/Web/Accessibility/Keyboard-navigable_JavaScript_widgets'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                trapType: trap.reason,
                hasEscapeRoute: trap.hasEscapeRoute,
                hasInstructions: trap.hasInstructions,
                severity: trap.severity,
              },
            },
          });
        });
      }

      if (warningTraps.length > 0) {
        score = Math.max(score - (warningTraps.length * 10), 60); // Reduce score for warnings
        
        warningTraps.forEach((trap) => {
          issues.push(`Potential keyboard trap: ${trap.reason}`);
          
          evidence.push({
            type: 'warning',
            description: 'Potential keyboard navigation issue',
            value: `Element: ${trap.element} - ${trap.reason}`,
            selector: trap.selector,
            elementCount: 1,
            affectedSelectors: [trap.selector],
            severity: 'warning',
            fixExample: {
              before: `<div>Custom focus handling without clear escape</div>`,
              after: `<div>
  <!-- Provide clear instructions -->
  <p id="instructions">Press Escape to exit this component</p>
  <div aria-describedby="instructions">Custom focus handling with escape</div>
</div>`,
              description: 'Provide clear escape instructions for custom focus management',
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/no-keyboard-trap.html'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                trapType: trap.reason,
                hasEscapeRoute: trap.hasEscapeRoute,
                hasInstructions: trap.hasInstructions,
                severity: trap.severity,
              },
            },
          });
        });
      }
    }

    // Add positive evidence for good keyboard navigation
    if (score > 80 && totalElements > 0) {
      evidence.push({
        type: 'info',
        description: 'No keyboard traps detected',
        value: `Analyzed ${totalElements} focusable elements with no keyboard traps found`,
        selector: 'body',
        elementCount: totalElements,
        affectedSelectors: ['*'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: totalElements,
          checkSpecificData: {
            focusableElements: trapAnalysis.focusableElementCount,
            customFocusHandlers: trapAnalysis.customFocusHandlers,
            modalElements: trapAnalysis.modalElements,
            potentialTraps: trapAnalysis.potentialTraps.length,
          },
        },
      });
    }

    // Generate recommendations
    if (score < 100) {
      recommendations.push('Ensure all interactive components allow keyboard navigation away');
      recommendations.push('Provide escape mechanisms (Escape key, close buttons) for modal dialogs');
      recommendations.push('Include clear instructions for custom keyboard interactions');
      recommendations.push('Test keyboard navigation thoroughly, especially in modal dialogs');
    } else {
      recommendations.push('Continue testing keyboard navigation in dynamic content');
      recommendations.push('Ensure new interactive components maintain keyboard accessibility');
    }

    if (trapAnalysis.modalElements > 0) {
      recommendations.push('Verify modal dialogs implement proper focus management');
      recommendations.push('Test that Escape key closes modal dialogs');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
