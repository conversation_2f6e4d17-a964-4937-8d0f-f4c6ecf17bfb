/**
 * WCAG-060: Unusual Words Check (3.1.3 Level AAA)
 * 60% Automated - Detects unusual words and jargon without definitions
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface UnusualWordsConfig extends EnhancedCheckConfig {
  enableAILanguageDetection?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAISemanticValidation?: boolean;
  enableVocabularyAnalysis?: boolean;
  enableAdvancedWordDetection?: boolean;
}

export class UnusualWordsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Common unusual word patterns and technical terms
  private readonly unusualWordPatterns = [
    // Technical jargon
    /\b(?:API|SDK|CLI|GUI|URL|HTTP|HTTPS|JSON|XML|CSS|HTML|JS|SQL|NoSQL|REST|SOAP|CRUD|MVC|SPA|PWA|CDN|DNS|SSL|TLS|VPN|SSH|FTP|SMTP|POP3|IMAP)\b/gi,
    // Business jargon
    /\b(?:synergy|leverage|paradigm|holistic|scalable|disruptive|innovative|cutting-edge|state-of-the-art|best-in-class|world-class|enterprise-grade|mission-critical|turnkey|end-to-end|omnichannel|B2B|B2C|SaaS|PaaS|IaaS|CRM|ERP|ROI|KPI|SLA|QoS)\b/gi,
    // Medical/Scientific terms
    /\b(?:algorithm|methodology|infrastructure|architecture|implementation|optimization|configuration|authentication|authorization|encryption|decryption|serialization|deserialization|polymorphism|encapsulation|inheritance|abstraction)\b/gi,
    // Acronyms without expansion
    /\b[A-Z]{2,}\b/g,
    // Technical suffixes
    /\w+(?:tion|sion|ment|ness|ity|ism|ology|graphy|metry)\b/gi,
  ];

  // Common words that should not be flagged
  private readonly commonWords = new Set([
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'under', 'over',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'cannot',
    'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'why', 'how', 'what', 'which', 'who', 'whom', 'whose',
    'all', 'any', 'some', 'many', 'much', 'few', 'little', 'more', 'most', 'less', 'least', 'every', 'each', 'both', 'either', 'neither',
    'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'first', 'second', 'third', 'last', 'next', 'previous',
    'good', 'bad', 'big', 'small', 'large', 'little', 'long', 'short', 'high', 'low', 'new', 'old', 'young', 'early', 'late', 'right', 'wrong', 'true', 'false',
    'home', 'work', 'school', 'house', 'car', 'book', 'page', 'time', 'day', 'week', 'month', 'year', 'hour', 'minute', 'second',
    'people', 'person', 'man', 'woman', 'child', 'family', 'friend', 'group', 'team', 'company', 'business', 'service', 'product', 'information', 'data', 'content'
  ]);

  async performCheck(config: UnusualWordsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: UnusualWordsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAILanguageDetection: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-060',
      'Unusual Words',
      'understandable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeUnusualWordsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with quality metrics
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-060',
        ruleName: 'Unusual Words',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.60,
          checkType: 'content-analysis',
          languageAnalysis: true,
          aiLanguageDetection: enhancedConfig.enableAILanguageDetection,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeUnusualWordsCheck(
    page: Page,
    config: UnusualWordsConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze text content for unusual words
    const textAnalysis = await page.evaluate(() => {
      const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, article, section, main, aside');
      const unusualWords: Array<{
        word: string;
        context: string;
        element: string;
        selector: string;
        hasDefinition: boolean;
        hasTooltip: boolean;
        hasAbbr: boolean;
        hasGlossary: boolean;
        frequency: number;
      }> = [];

      const wordFrequency = new Map<string, number>();
      const wordContexts = new Map<string, Array<{ context: string; element: string; selector: string }>>();

      // Patterns for unusual words (simplified for browser context)
      const technicalPattern = /\b(?:API|SDK|CLI|GUI|URL|HTTP|HTTPS|JSON|XML|CSS|HTML|JS|SQL|REST|SOAP|CRUD|MVC|SPA|PWA|CDN|DNS|SSL|TLS|VPN|SSH|FTP|SMTP)\b/gi;
      const businessPattern = /\b(?:synergy|leverage|paradigm|holistic|scalable|disruptive|innovative|cutting-edge|state-of-the-art|best-in-class|world-class|enterprise-grade|mission-critical|turnkey|end-to-end|omnichannel|B2B|B2C|SaaS|PaaS|IaaS|CRM|ERP|ROI|KPI|SLA|QoS)\b/gi;
      const acronymPattern = /\b[A-Z]{2,}\b/g;

      textElements.forEach((element, index) => {
        const text = element.textContent || '';
        if (text.trim().length < 10) return; // Skip very short text

        const selector = generateSelector(element, index);
        
        // Find technical terms
        [technicalPattern, businessPattern, acronymPattern].forEach(pattern => {
          let match;
          while ((match = pattern.exec(text)) !== null) {
            const word = match[0].toLowerCase();
            
            // Skip common words
            if (isCommonWord(word)) continue;
            
            // Check if word has definition nearby
            const hasDefinition = checkForDefinition(element, word);
            const hasTooltip = element.hasAttribute('title') || element.querySelector(`[title*="${word}"]`) !== null;
            const hasAbbr = element.querySelector(`abbr[title*="${word}"], acronym[title*="${word}"]`) !== null;
            const hasGlossary = checkForGlossaryLink(element, word);

            // Get context (surrounding text)
            const wordIndex = text.toLowerCase().indexOf(word);
            const contextStart = Math.max(0, wordIndex - 30);
            const contextEnd = Math.min(text.length, wordIndex + word.length + 30);
            const context = text.substring(contextStart, contextEnd).trim();

            // Track frequency
            const currentFreq = wordFrequency.get(word) || 0;
            wordFrequency.set(word, currentFreq + 1);

            // Track contexts
            if (!wordContexts.has(word)) {
              wordContexts.set(word, []);
            }
            wordContexts.get(word)!.push({
              context,
              element: element.tagName.toLowerCase(),
              selector,
            });
          }
        });
      });

      // Process collected words
      wordFrequency.forEach((frequency, word) => {
        const contexts = wordContexts.get(word) || [];
        const firstContext = contexts[0];
        
        if (firstContext) {
          const element = document.querySelector(firstContext.selector);
          const hasDefinition = element ? checkForDefinition(element, word) : false;
          const hasTooltip = element ? (element.hasAttribute('title') || element.querySelector(`[title*="${word}"]`) !== null) : false;
          const hasAbbr = element ? (element.querySelector(`abbr[title*="${word}"], acronym[title*="${word}"]`) !== null) : false;
          const hasGlossary = element ? checkForGlossaryLink(element, word) : false;

          unusualWords.push({
            word,
            context: firstContext.context,
            element: firstContext.element,
            selector: firstContext.selector,
            hasDefinition,
            hasTooltip,
            hasAbbr,
            hasGlossary,
            frequency,
          });
        }
      });

      return {
        unusualWords,
        totalTextElements: textElements.length,
        totalWords: Array.from(wordFrequency.values()).reduce((sum, freq) => sum + freq, 0),
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function isCommonWord(word: string): boolean {
        const commonWords = new Set([
          'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
          'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can',
          'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'why', 'how', 'what', 'which', 'who', 'whom', 'whose',
          'all', 'any', 'some', 'many', 'much', 'few', 'little', 'more', 'most', 'less', 'least', 'every', 'each', 'both', 'either', 'neither',
          'good', 'bad', 'big', 'small', 'large', 'little', 'long', 'short', 'high', 'low', 'new', 'old', 'young', 'early', 'late', 'right', 'wrong', 'true', 'false',
          'home', 'work', 'school', 'house', 'car', 'book', 'page', 'time', 'day', 'week', 'month', 'year', 'hour', 'minute', 'second',
          'people', 'person', 'man', 'woman', 'child', 'family', 'friend', 'group', 'team', 'company', 'business', 'service', 'product', 'information', 'data', 'content'
        ]);
        return commonWords.has(word.toLowerCase()) || word.length < 3;
      }

      function checkForDefinition(element: Element, word: string): boolean {
        // Check for definition patterns in the same element or nearby
        const text = element.textContent || '';
        const definitionPatterns = [
          new RegExp(`${word}\\s*(?:is|means|refers to|stands for|defined as)`, 'i'),
          new RegExp(`(?:is|means|refers to|stands for|defined as)\\s*${word}`, 'i'),
          new RegExp(`${word}\\s*\\([^)]+\\)`, 'i'), // Word followed by parenthetical explanation
        ];

        return definitionPatterns.some(pattern => pattern.test(text));
      }

      function checkForGlossaryLink(element: Element, word: string): boolean {
        // Check for links to glossary or definition pages
        const links = element.querySelectorAll('a[href]');
        return Array.from(links).some(link => {
          const href = link.getAttribute('href') || '';
          const linkText = link.textContent || '';
          return (href.includes('glossary') || href.includes('definition') || href.includes('terms')) &&
                 linkText.toLowerCase().includes(word.toLowerCase());
        });
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = textAnalysis.unusualWords.length;

    if (elementCount > 0) {
      // Filter words that don't have definitions
      const wordsWithoutDefinitions = textAnalysis.unusualWords.filter(
        word => !word.hasDefinition && !word.hasTooltip && !word.hasAbbr && !word.hasGlossary
      );

      if (wordsWithoutDefinitions.length > 0) {
        // Calculate score based on number of undefined unusual words
        const penalty = Math.min(60, wordsWithoutDefinitions.length * 5);
        score -= penalty;
        
        issues.push(`${wordsWithoutDefinitions.length} unusual words lack definitions or explanations`);

        evidence.push({
          type: 'content',
          description: 'Unusual words without definitions',
          value: `Found ${wordsWithoutDefinitions.length} unusual words that may need definitions for AAA compliance`,
          elementCount: wordsWithoutDefinitions.length,
          affectedSelectors: wordsWithoutDefinitions.map(word => word.selector),
          severity: 'warning',
          fixExample: {
            before: 'Our API provides seamless integration.',
            after: 'Our API (Application Programming Interface) provides seamless integration.',
            description: 'Provide definitions for unusual words, jargon, and acronyms',
            codeExample: `
<!-- Before: Undefined technical terms -->
<p>Our SaaS platform leverages cutting-edge APIs for optimal ROI.</p>

<!-- After: With definitions -->
<p>Our <abbr title="Software as a Service">SaaS</abbr> platform leverages cutting-edge 
<abbr title="Application Programming Interfaces">APIs</abbr> for optimal 
<abbr title="Return on Investment">ROI</abbr>.</p>

<!-- Alternative: With glossary links -->
<p>Our <a href="/glossary#saas">SaaS</a> platform leverages cutting-edge 
<a href="/glossary#api">APIs</a> for optimal <a href="/glossary#roi">ROI</a>.</p>

<!-- Alternative: With inline definitions -->
<p>Our SaaS (Software as a Service) platform leverages cutting-edge 
APIs (Application Programming Interfaces) for optimal ROI (Return on Investment).</p>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/unusual-words.html',
              'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/abbr',
              'https://www.w3.org/WAI/WCAG21/Techniques/html/H28'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: textAnalysis.totalTextElements,
            checkSpecificData: {
              totalUnusualWords: elementCount,
              wordsWithoutDefinitions: wordsWithoutDefinitions.length,
              wordsWithDefinitions: elementCount - wordsWithoutDefinitions.length,
              mostFrequentWords: textAnalysis.unusualWords
                .sort((a, b) => b.frequency - a.frequency)
                .slice(0, 5)
                .map(w => ({ word: w.word, frequency: w.frequency })),
            },
          },
        });

        // Add specific examples for words without definitions
        wordsWithoutDefinitions.slice(0, 10).forEach(wordInfo => {
          evidence.push({
            type: 'content',
            description: `Unusual word without definition: "${wordInfo.word}"`,
            value: `Context: "${wordInfo.context}"`,
            selector: wordInfo.selector,
            severity: 'warning',
            metadata: {
              checkSpecificData: {
                word: wordInfo.word,
                frequency: wordInfo.frequency,
                hasDefinition: wordInfo.hasDefinition,
                hasTooltip: wordInfo.hasTooltip,
                hasAbbr: wordInfo.hasAbbr,
                hasGlossary: wordInfo.hasGlossary,
              },
            },
          });
        });

        recommendations.push('Provide definitions for unusual words, jargon, and technical terms');
        recommendations.push('Use <abbr> elements with title attributes for acronyms');
        recommendations.push('Consider creating a glossary page for frequently used technical terms');
        recommendations.push('Provide inline definitions or explanations for specialized vocabulary');
        recommendations.push('Link unusual words to their definitions in a glossary');
      } else {
        // All unusual words have definitions
        evidence.push({
          type: 'info',
          description: 'Unusual words with definitions found',
          value: `Found ${elementCount} unusual words, all with appropriate definitions or explanations`,
          elementCount,
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: textAnalysis.totalTextElements,
            checkSpecificData: {
              totalUnusualWords: elementCount,
              allHaveDefinitions: true,
            },
          },
        });
      }
    } else {
      // No unusual words found
      evidence.push({
        type: 'info',
        description: 'No unusual words detected',
        value: 'Page content uses common vocabulary without unusual words or jargon',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: textAnalysis.totalTextElements,
          checkSpecificData: {
            noUnusualWords: true,
            totalWords: textAnalysis.totalWords,
          },
        },
      });
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
