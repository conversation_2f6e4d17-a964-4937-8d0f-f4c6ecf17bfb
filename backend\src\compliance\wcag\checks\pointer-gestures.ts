/**
 * WCAG-053: Pointer Gestures Check
 * Success Criterion: 2.5.1 Pointer Gestures (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface GesturePatternDetection {
  multiPointGestures: number;
  pathBasedGestures: number;
  complexGestures: number;
  alternativeInputs: number;
  gestureTypes: string[];
  riskLevel: 'none' | 'low' | 'medium' | 'high';
  detectionConfidence: number;
}

interface AlternativeInputValidation {
  hasKeyboardAlternatives: boolean;
  hasSinglePointAlternatives: boolean;
  hasButtonAlternatives: boolean;
  alternativeCount: number;
  missingAlternatives: string[];
}

interface GestureComplexityAnalysis {
  simpleGestures: number;
  complexGestures: number;
  multiStepGestures: number;
  simultaneousGestures: number;
  complexityScore: number;
  accessibilityRating: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface PointerGesturesConfig extends EnhancedCheckConfig {
  enableGesturePatternDetection?: boolean;
  enableAlternativeInputValidation?: boolean;
  enableGestureComplexityAnalysis?: boolean;
  enableDragDropAlternatives?: boolean;
  enableAdvancedGestureDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class PointerGesturesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();

  async performCheck(config: PointerGesturesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized gesture detection
    const enhancedConfig: PointerGesturesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000, // Target: <2s performance
      },
      enableGesturePatternDetection: true,
      enableAlternativeInputValidation: true,
      enableGestureComplexityAnalysis: true,
      enableDragDropAlternatives: true,
      enableAdvancedGestureDetection: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-053',
      'Pointer Gestures',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executePointerGesturesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with pointer gesture analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-049',
        ruleName: 'Pointer Gestures',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'pointer-gesture-analysis',
          gestureDetection: true,
          alternativeInputValidation: true,
          advancedGestureDetection: enhancedConfig.enableAdvancedGestureDetection,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executePointerGesturesCheck(
    page: Page,
    _config: PointerGesturesConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Gesture Pattern Detection Algorithm - Advanced Implementation
    const gesturePatternDetection = await this.executeGesturePatternDetection(page);

    // Alternative Input Validation Algorithm
    const alternativeInputValidation = await this.validateAlternativeInputs(page);

    // Gesture Complexity Analysis Algorithm
    const gestureComplexityAnalysis = await this.analyzeGestureComplexity(page);

    // Drag & Drop Alternatives Algorithm
    const dragDropAlternatives = await this.analyzeDragDropAlternatives(page);

    // Combine all specialized detection results
    const allAnalyses = [
      gesturePatternDetection,
      alternativeInputValidation,
      gestureComplexityAnalysis,
      dragDropAlternatives,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 82-85% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Gesture Pattern Detection Algorithm - Core Implementation
   * Target: 82-85% gesture pattern detection accuracy
   */
  private async executeGesturePatternDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const gestureDetection = await page.evaluate((): GesturePatternDetection => {
      // Advanced gesture pattern detection
      const gestureSelectors = [
        '[ontouchstart]', '[ontouchmove]', '[ontouchend]',
        '[onpointerdown]', '[onpointermove]', '[onpointerup]',
        '.swipeable', '.pinchable', '.rotatable', '.gesture-enabled',
        '[data-gesture]', '[data-swipe]', '[data-pinch]', '[data-rotate]',
        '.carousel', '.slider', '.gallery', '.zoom-container'
      ];

      const gestureElements = document.querySelectorAll(gestureSelectors.join(', '));
      const gestureTypes: string[] = [];
      let multiPointGestures = 0;
      let pathBasedGestures = 0;
      let complexGestures = 0;
      let alternativeInputs = 0;

      gestureElements.forEach(element => {
        const classList = Array.from(element.classList);
        const attributes = Array.from(element.attributes).map(attr => attr.name);

        // Detect multi-point gestures (pinch, zoom, rotate)
        if (classList.some(cls => ['pinchable', 'rotatable', 'zoom'].some(gesture => cls.includes(gesture))) ||
            attributes.some(attr => ['pinch', 'rotate', 'zoom'].some(gesture => attr.includes(gesture)))) {
          multiPointGestures++;
          gestureTypes.push('multi-point');
        }

        // Detect path-based gestures (swipe, drag)
        if (classList.some(cls => ['swipeable', 'draggable', 'sortable'].some(gesture => cls.includes(gesture))) ||
            attributes.some(attr => ['swipe', 'drag', 'sort'].some(gesture => attr.includes(gesture)))) {
          pathBasedGestures++;
          gestureTypes.push('path-based');
        }

        // Detect complex gestures (multi-step, simultaneous)
        if (classList.some(cls => ['gesture-complex', 'multi-step', 'simultaneous'].some(gesture => cls.includes(gesture)))) {
          complexGestures++;
          gestureTypes.push('complex');
        }

        // Check for alternative inputs
        const hasKeyboardSupport = element.hasAttribute('tabindex') ||
                                  element.querySelector('[tabindex]') !== null;
        const hasButtonAlternative = element.querySelector('button, [role="button"]') !== null;
        const hasClickAlternative = element.hasAttribute('onclick') ||
                                   element.addEventListener !== undefined;

        if (hasKeyboardSupport || hasButtonAlternative || hasClickAlternative) {
          alternativeInputs++;
        }
      });

      // Calculate risk level
      const totalGestures = multiPointGestures + pathBasedGestures + complexGestures;
      const alternativeRatio = totalGestures > 0 ? alternativeInputs / totalGestures : 1;

      let riskLevel: GesturePatternDetection['riskLevel'] = 'none';
      let detectionConfidence = 0.8;

      if (totalGestures === 0) {
        riskLevel = 'none';
        detectionConfidence = 0.9;
      } else if (alternativeRatio >= 0.8) {
        riskLevel = 'low';
        detectionConfidence = 0.85;
      } else if (alternativeRatio >= 0.5) {
        riskLevel = 'medium';
        detectionConfidence = 0.82;
      } else {
        riskLevel = 'high';
        detectionConfidence = 0.8;
      }

      return {
        multiPointGestures,
        pathBasedGestures,
        complexGestures,
        alternativeInputs,
        gestureTypes: [...new Set(gestureTypes)],
        riskLevel,
        detectionConfidence,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (gestureDetection.riskLevel === 'none' || gestureDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Gesture pattern detection: Low or no accessibility risk',
        value: `Risk level: ${gestureDetection.riskLevel}, Confidence: ${(gestureDetection.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Gesture accessibility risk: ${gestureDetection.riskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Gesture pattern detection: ${gestureDetection.riskLevel} risk`,
        value: `Multi-point: ${gestureDetection.multiPointGestures}, Path-based: ${gestureDetection.pathBasedGestures}, Complex: ${gestureDetection.complexGestures}, Alternatives: ${gestureDetection.alternativeInputs}`,
        severity: gestureDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Add alternative input methods for all gesture-based interactions');
    }

    // Report detected gesture types
    if (gestureDetection.gestureTypes.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Detected gesture types',
        value: gestureDetection.gestureTypes.join(', '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Alternative Input Validation Algorithm
   */
  private async validateAlternativeInputs(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const alternativeValidation = await page.evaluate((): AlternativeInputValidation => {
      const gestureElements = document.querySelectorAll(
        '[ontouchstart], [ontouchmove], .swipeable, .pinchable, .rotatable, .gesture-enabled, .draggable'
      );

      let hasKeyboardAlternatives = true;
      let hasSinglePointAlternatives = true;
      let hasButtonAlternatives = true;
      let alternativeCount = 0;
      const missingAlternatives: string[] = [];

      gestureElements.forEach((element, index) => {
        const elementAlternatives = {
          keyboard: false,
          singlePoint: false,
          button: false,
        };

        // Check for keyboard alternatives
        if (element.hasAttribute('tabindex') ||
            element.querySelector('[tabindex]') !== null ||
            element.hasAttribute('onkeydown') ||
            element.hasAttribute('onkeyup')) {
          elementAlternatives.keyboard = true;
        }

        // Check for single-point alternatives (click, tap)
        if (element.hasAttribute('onclick') ||
            element.hasAttribute('onpointerdown') ||
            element.querySelector('button, [role="button"]') !== null) {
          elementAlternatives.singlePoint = true;
        }

        // Check for button alternatives
        if (element.querySelector('button, [role="button"], input[type="button"]') !== null ||
            element.closest('form') !== null) {
          elementAlternatives.button = true;
        }

        // Count alternatives for this element
        const elementAlternativeCount = Object.values(elementAlternatives).filter(Boolean).length;
        alternativeCount += elementAlternativeCount;

        // Track missing alternatives
        if (!elementAlternatives.keyboard) {
          hasKeyboardAlternatives = false;
          missingAlternatives.push(`Element ${index + 1}: keyboard alternative`);
        }
        if (!elementAlternatives.singlePoint) {
          hasSinglePointAlternatives = false;
          missingAlternatives.push(`Element ${index + 1}: single-point alternative`);
        }
        if (!elementAlternatives.button) {
          hasButtonAlternatives = false;
          missingAlternatives.push(`Element ${index + 1}: button alternative`);
        }
      });

      return {
        hasKeyboardAlternatives,
        hasSinglePointAlternatives,
        hasButtonAlternatives,
        alternativeCount,
        missingAlternatives,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (alternativeValidation.hasKeyboardAlternatives && alternativeValidation.hasSinglePointAlternatives) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Alternative input validation: Sufficient alternatives available',
        value: `Keyboard: ${alternativeValidation.hasKeyboardAlternatives}, Single-point: ${alternativeValidation.hasSinglePointAlternatives}, Button: ${alternativeValidation.hasButtonAlternatives}`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient alternative input methods for gesture-based interactions');
      evidence.push({
        type: 'code',
        description: 'Alternative input validation: Missing alternatives',
        value: `Missing alternatives: ${alternativeValidation.missingAlternatives.slice(0, 3).join('; ')}`,
        severity: 'error',
      });
      recommendations.push('Add keyboard and single-point alternatives for all gesture interactions');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Gesture Complexity Analysis Algorithm
   */
  private async analyzeGestureComplexity(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const complexityAnalysis = await page.evaluate((): GestureComplexityAnalysis => {
      const gestureElements = document.querySelectorAll(
        '[data-gesture], .gesture-enabled, .swipeable, .pinchable, .rotatable, .draggable'
      );

      let simpleGestures = 0;
      let complexGestures = 0;
      let multiStepGestures = 0;
      let simultaneousGestures = 0;

      gestureElements.forEach(element => {
        const classList = Array.from(element.classList);
        const dataAttributes = Array.from(element.attributes)
          .filter(attr => attr.name.startsWith('data-'))
          .map(attr => attr.name);

        // Simple gestures (single action)
        if (classList.some(cls => ['swipeable', 'clickable', 'tappable'].some(simple => cls.includes(simple)))) {
          simpleGestures++;
        }

        // Complex gestures (multi-point, path-dependent)
        if (classList.some(cls => ['pinchable', 'rotatable', 'zoom'].some(complex => cls.includes(complex))) ||
            dataAttributes.some(attr => ['pinch', 'rotate', 'zoom'].some(complex => attr.includes(complex)))) {
          complexGestures++;
        }

        // Multi-step gestures
        if (classList.some(cls => ['multi-step', 'sequence', 'chain'].some(multi => cls.includes(multi))) ||
            dataAttributes.some(attr => ['step', 'sequence', 'chain'].some(multi => attr.includes(multi)))) {
          multiStepGestures++;
        }

        // Simultaneous gestures
        if (classList.some(cls => ['simultaneous', 'concurrent', 'parallel'].some(sim => cls.includes(sim))) ||
            dataAttributes.some(attr => ['simultaneous', 'concurrent', 'parallel'].some(sim => attr.includes(sim)))) {
          simultaneousGestures++;
        }
      });

      // Calculate complexity score (0-1, where 0 is simple, 1 is very complex)
      const totalGestures = simpleGestures + complexGestures + multiStepGestures + simultaneousGestures;
      const complexityScore = totalGestures > 0 ?
        (complexGestures * 0.3 + multiStepGestures * 0.5 + simultaneousGestures * 0.7) / totalGestures : 0;

      // Determine accessibility rating
      let accessibilityRating: GestureComplexityAnalysis['accessibilityRating'] = 'excellent';
      if (complexityScore > 0.7) {
        accessibilityRating = 'poor';
      } else if (complexityScore > 0.4) {
        accessibilityRating = 'fair';
      } else if (complexityScore > 0.2) {
        accessibilityRating = 'good';
      }

      return {
        simpleGestures,
        complexGestures,
        multiStepGestures,
        simultaneousGestures,
        complexityScore,
        accessibilityRating,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (complexityAnalysis.accessibilityRating === 'excellent' || complexityAnalysis.accessibilityRating === 'good') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Gesture complexity analysis: Good accessibility rating',
        value: `Rating: ${complexityAnalysis.accessibilityRating}, Complexity score: ${(complexityAnalysis.complexityScore * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Gesture complexity too high: ${complexityAnalysis.accessibilityRating} accessibility rating`);
      evidence.push({
        type: 'code',
        description: `Gesture complexity analysis: ${complexityAnalysis.accessibilityRating} rating`,
        value: `Simple: ${complexityAnalysis.simpleGestures}, Complex: ${complexityAnalysis.complexGestures}, Multi-step: ${complexityAnalysis.multiStepGestures}, Simultaneous: ${complexityAnalysis.simultaneousGestures}`,
        severity: complexityAnalysis.accessibilityRating === 'poor' ? 'error' : 'warning',
      });
      recommendations.push('Simplify gesture interactions or provide simpler alternatives');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Drag & Drop Alternatives Algorithm
   */
  private async analyzeDragDropAlternatives(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const dragDropAnalysis = await page.$$eval(
      '[draggable="true"], .draggable, .sortable, .droppable, [ondragstart], [ondrop]',
      (elements) => {
        return elements.map((element, index) => {
          const isDraggable = element.hasAttribute('draggable') || element.classList.contains('draggable');
          const isDroppable = element.classList.contains('droppable') || element.hasAttribute('ondrop');
          const isSortable = element.classList.contains('sortable');

          // Check for alternative methods
          const hasKeyboardSupport = element.hasAttribute('tabindex') ||
                                   element.querySelector('[tabindex]') !== null;
          const hasButtonControls = element.querySelector('.move-up, .move-down, .move-left, .move-right, .sort-button') !== null;
          const hasFormAlternative = element.closest('form') !== null ||
                                   element.querySelector('select, input') !== null;
          const hasContextMenu = element.hasAttribute('oncontextmenu') ||
                                element.querySelector('.context-menu') !== null;

          const alternativeCount = [hasKeyboardSupport, hasButtonControls, hasFormAlternative, hasContextMenu]
            .filter(Boolean).length;

          return {
            index,
            selector: `draggable-${index}`,
            isDraggable,
            isDroppable,
            isSortable,
            hasKeyboardSupport,
            hasButtonControls,
            hasFormAlternative,
            hasContextMenu,
            alternativeCount,
            hasAdequateAlternatives: alternativeCount >= 1,
          };
        });
      }
    );

    const totalChecks = dragDropAnalysis.length;
    let passedChecks = 0;

    dragDropAnalysis.forEach((element, index) => {
      if (element.hasAdequateAlternatives) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Drag & drop element ${index + 1} has adequate alternatives`,
          value: `${element.selector} - ${element.alternativeCount} alternative methods available`,
          severity: 'info',
        });
      } else {
        issues.push(`Drag & drop element ${index + 1} lacks alternative input methods`);
        evidence.push({
          type: 'code',
          description: `Drag & drop element ${index + 1} needs alternatives`,
          value: `${element.selector} - draggable: ${element.isDraggable}, alternatives: ${element.alternativeCount}`,
          severity: 'error',
        });
        recommendations.push(`Add keyboard or button alternatives for drag & drop element ${index + 1}`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

}

      // Check for touch event handlers that might implement gestures
      const elementsWithTouch = document.querySelectorAll(`
        [ontouchstart], [ontouchmove], [ontouchend],
        [onpointerdown], [onpointermove], [onpointerup],
        .swipeable, .pinchable, .rotatable, .gesture-enabled
      `);

      elementsWithTouch.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'warning';
        let gestureType = 'unknown';
        let hasAlternative = false;
        let alternativeType = 'none';

        // Check for swipe gestures
        if (element.classList.contains('swipeable') || 
            element.hasAttribute('ontouchmove')) {
          gestureType = 'swipe';
          
          // Look for alternative navigation
          const hasButtons = element.querySelector('button, [role="button"], .nav-button') !== null;
          const hasKeyboardNav = element.hasAttribute('onkeydown');
          
          if (hasButtons) {
            hasAlternative = true;
            alternativeType = 'buttons';
          } else if (hasKeyboardNav) {
            hasAlternative = true;
            alternativeType = 'keyboard';
          } else {
            issues.push('Swipe gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for pinch/zoom gestures
        if (element.classList.contains('pinchable') ||
            element.classList.contains('zoomable')) {
          gestureType = 'pinch';
          
          // Look for zoom controls
          const hasZoomControls = element.querySelector('.zoom-in, .zoom-out, [aria-label*="zoom"]') !== null;
          
          if (hasZoomControls) {
            hasAlternative = true;
            alternativeType = 'zoom-controls';
          } else {
            issues.push('Pinch/zoom gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for rotation gestures
        if (element.classList.contains('rotatable')) {
          gestureType = 'rotation';
          
          // Look for rotation controls
          const hasRotateControls = element.querySelector('.rotate-left, .rotate-right, [aria-label*="rotate"]') !== null;
          
          if (hasRotateControls) {
            hasAlternative = true;
            alternativeType = 'rotate-controls';
          } else {
            issues.push('Rotation gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for drag gestures
        if (element.hasAttribute('draggable') || 
            element.classList.contains('draggable')) {
          gestureType = 'drag';
          
          // Look for keyboard alternatives
          const hasKeyboardDrag = element.hasAttribute('onkeydown') ||
                                 element.querySelector('[role="button"]') !== null;
          
          if (hasKeyboardDrag) {
            hasAlternative = true;
            alternativeType = 'keyboard';
          } else {
            issues.push('Drag gesture without keyboard alternative');
            severity = 'error';
          }
        }

        if (issues.length > 0 || gestureType !== 'unknown') {
          problematicGestures.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            gestureType,
            hasAlternative,
            alternativeType,
            issues,
            severity,
          });
        }
      });

      // Check JavaScript for gesture libraries
      const scripts = document.querySelectorAll('script');
      let hasGestureLibrary = false;
      const gestureLibraries = ['hammer.js', 'interact.js', 'gesture', 'swipe', 'pinch'];
      
      scripts.forEach((script) => {
        const content = script.textContent || '';
        const src = script.src || '';
        
        if (gestureLibraries.some(lib => content.includes(lib) || src.includes(lib))) {
          hasGestureLibrary = true;
        }
      });

      if (hasGestureLibrary) {
        problematicGestures.push({
          selector: 'script',
          gestureType: 'library',
          hasAlternative: false, // Cannot determine from static analysis
          alternativeType: 'unknown',
          issues: ['Gesture library detected - verify single-pointer alternatives'],
          severity: 'warning',
        });
      }

      return {
        problematicGestures,
        totalGestureElements: elementsWithTouch.length,
        problematicCount: problematicGestures.length,
        withoutAlternatives: problematicGestures.filter(g => !g.hasAlternative).length,
        hasGestureLibrary,
      };
    });

    let score = 100;
    const elementCount = gestureAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      gestureAnalysis.problematicGestures.forEach((gesture) => {
        const deduction = gesture.severity === 'error' ? 20 : 
                         gesture.severity === 'warning' ? 10 : 5;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with potential gesture issues found`);
      if (gestureAnalysis.withoutAlternatives > 0) {
        issues.push(`${gestureAnalysis.withoutAlternatives} gestures without single-pointer alternatives`);
      }

      gestureAnalysis.problematicGestures.forEach((gesture) => {
        evidence.push({
          type: 'code',
          description: `Pointer gesture issue: ${gesture.issues.join(', ')}`,
          value: `${gesture.gestureType} gesture | Alternative: ${gesture.alternativeType}`,
          selector: gesture.selector,
          elementCount: 1,
          affectedSelectors: [gesture.selector],
          severity: gesture.severity,
          fixExample: {
            before: this.getBeforeExample(gesture),
            after: this.getAfterExample(gesture),
            description: this.getFixDescription(gesture.gestureType),
            codeExample: this.getCodeExample(gesture.gestureType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/pointer-gestures.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G215',
              'https://www.w3.org/WAI/WCAG21/Techniques/G216'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              gestureType: gesture.gestureType,
              hasAlternative: gesture.hasAlternative,
              alternativeType: gesture.alternativeType,
              issues: gesture.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Provide single-pointer alternatives for multipoint gestures');
    recommendations.push('Ensure path-based gestures have simple alternatives');
    recommendations.push('Add buttons or controls for swipe, pinch, and rotation actions');
    recommendations.push('Test functionality with single-pointer input only');
    recommendations.push('Consider users with limited dexterity or mobility');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(gesture: any): string {
    switch (gesture.gestureType) {
      case 'swipe':
        return '<div class="swipeable" ontouchmove="handleSwipe()">Swipe to navigate</div>';
      case 'pinch':
        return '<div class="pinchable">Pinch to zoom</div>';
      case 'rotation':
        return '<div class="rotatable">Rotate to adjust</div>';
      case 'drag':
        return '<div draggable="true">Drag to reorder</div>';
      default:
        return '<div class="gesture-enabled">Gesture-controlled element</div>';
    }
  }

  private getAfterExample(gesture: any): string {
    switch (gesture.gestureType) {
      case 'swipe':
        return '<div class="carousel">\n  <button class="prev">Previous</button>\n  <div class="content">Content</div>\n  <button class="next">Next</button>\n</div>';
      case 'pinch':
        return '<div class="zoomable">\n  <button class="zoom-out">-</button>\n  <div class="content">Content</div>\n  <button class="zoom-in">+</button>\n</div>';
      case 'rotation':
        return '<div class="rotatable">\n  <button class="rotate-left">↺</button>\n  <div class="content">Content</div>\n  <button class="rotate-right">↻</button>\n</div>';
      case 'drag':
        return '<div class="sortable">\n  <button onclick="moveUp()">↑</button>\n  <div class="item">Item</div>\n  <button onclick="moveDown()">↓</button>\n</div>';
      default:
        return '<div class="accessible-control">Element with single-pointer alternative</div>';
    }
  }

  private getFixDescription(gestureType: string): string {
    switch (gestureType) {
      case 'swipe':
        return 'Add navigation buttons as alternative to swipe gestures';
      case 'pinch':
        return 'Provide zoom controls as alternative to pinch gestures';
      case 'rotation':
        return 'Add rotation buttons as alternative to rotation gestures';
      case 'drag':
        return 'Provide keyboard or button alternatives to drag operations';
      default:
        return 'Ensure single-pointer alternatives are available';
    }
  }

  private getCodeExample(gestureType: string): string {
    switch (gestureType) {
      case 'swipe':
        return `
<!-- Before: Swipe-only navigation -->
<div class="carousel" ontouchmove="handleSwipe(event)">
  <div class="slide">Slide 1</div>
  <div class="slide">Slide 2</div>
  <div class="slide">Slide 3</div>
</div>

<!-- After: Swipe with button alternatives -->
<div class="carousel">
  <button class="nav-btn prev" onclick="previousSlide()" aria-label="Previous slide">‹</button>
  <div class="slides" ontouchmove="handleSwipe(event)">
    <div class="slide">Slide 1</div>
    <div class="slide">Slide 2</div>
    <div class="slide">Slide 3</div>
  </div>
  <button class="nav-btn next" onclick="nextSlide()" aria-label="Next slide">›</button>
  <div class="indicators">
    <button onclick="goToSlide(0)" aria-label="Go to slide 1">1</button>
    <button onclick="goToSlide(1)" aria-label="Go to slide 2">2</button>
    <button onclick="goToSlide(2)" aria-label="Go to slide 3">3</button>
  </div>
</div>
        `;
      case 'pinch':
        return `
<!-- Before: Pinch-only zoom -->
<div class="image-viewer" ontouchstart="handlePinch(event)">
  <img src="image.jpg" alt="Zoomable image">
</div>

<!-- After: Pinch with zoom controls -->
<div class="image-viewer">
  <div class="zoom-controls">
    <button onclick="zoomOut()" aria-label="Zoom out">-</button>
    <span class="zoom-level">100%</span>
    <button onclick="zoomIn()" aria-label="Zoom in">+</button>
    <button onclick="resetZoom()" aria-label="Reset zoom">Reset</button>
  </div>
  <div class="image-container" ontouchstart="handlePinch(event)">
    <img src="image.jpg" alt="Zoomable image">
  </div>
</div>
        `;
      default:
        return `
<!-- General principle: Always provide single-pointer alternatives -->

<!-- Multi-touch gesture with single-pointer alternative -->
<div class="interactive-element">
  <!-- Gesture-enabled area -->
  <div class="gesture-area" 
       ontouchstart="handleGesture(event)"
       onpointerdown="handlePointer(event)">
    Content
  </div>
  
  <!-- Single-pointer alternatives -->
  <div class="controls">
    <button onclick="action1()">Action 1</button>
    <button onclick="action2()">Action 2</button>
    <button onclick="action3()">Action 3</button>
  </div>
</div>

<!-- Keyboard alternatives for complex gestures -->
<div class="sortable-list" onkeydown="handleKeyboardSort(event)">
  <div class="item" draggable="true" tabindex="0">
    Item 1
    <button onclick="moveUp(0)">↑</button>
    <button onclick="moveDown(0)">↓</button>
  </div>
</div>
        `;
    }
  }
}
