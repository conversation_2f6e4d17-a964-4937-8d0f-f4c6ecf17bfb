/**
 * WCAG-044: Timing Adjustable Check
 * Success Criterion: 2.2.1 Timing Adjustable (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface TimingPatternDetection {
  timeoutElements: number;
  sessionTimers: number;
  autoRefreshElements: number;
  countdownTimers: number;
  timingPatterns: string[];
  riskLevel: 'none' | 'low' | 'medium' | 'high';
  detectionConfidence: number;
}

interface TimeoutControlValidation {
  hasTimeoutControls: boolean;
  hasExtendOption: boolean;
  hasDisableOption: boolean;
  hasWarningSystem: boolean;
  controlTypes: string[];
  adequateControls: boolean;
}

interface SessionManagementAnalysis {
  hasSessionTimeout: boolean;
  timeoutDuration: number;
  hasWarningBeforeTimeout: boolean;
  hasDataPersistence: boolean;
  hasAutoSave: boolean;
  sessionRiskLevel: 'low' | 'medium' | 'high';
}

export interface TimingAdjustableConfig extends EnhancedCheckConfig {
  enableTimingPatternDetection?: boolean;
  enableTimeoutControlValidation?: boolean;
  enableSessionManagementAnalysis?: boolean;
  enableAutoRefreshDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
  enableComponentLibraryDetection?: boolean;
}

export class TimingAdjustableCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();

  async performCheck(config: TimingAdjustableConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized timing detection
    const enhancedConfig: TimingAdjustableConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000, // Target: <2s performance
      },
      enableTimingPatternDetection: true,
      enableTimeoutControlValidation: true,
      enableSessionManagementAnalysis: true,
      enableAutoRefreshDetection: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
      enableComponentLibraryDetection: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-044',
      'Timing Adjustable',
      'operable',
      0.0687,
      'A',
      enhancedConfig,
      this.executeTimingAdjustableCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with timing analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-044',
        ruleName: 'Timing Adjustable',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'timing-analysis',
          timeoutDetection: true,
          timingControlValidation: true,
          sessionManagementAnalysis: true,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          componentLibraryDetection: enhancedConfig.enableComponentLibraryDetection,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeTimingAdjustableCheck(
    page: Page,
    _config: TimingAdjustableConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Timing Pattern Detection Algorithm - Advanced Implementation
    const timingPatternDetection = await this.executeTimingPatternDetection(page);

    // Timeout Control Validation Algorithm
    const timeoutControlValidation = await this.validateTimeoutControls(page);

    // Session Management Analysis Algorithm
    const sessionManagementAnalysis = await this.analyzeSessionManagement(page);

    // Auto-Refresh Detection Algorithm
    const autoRefreshDetection = await this.detectAutoRefresh(page);

    // Combine all specialized detection results
    const allAnalyses = [
      timingPatternDetection,
      timeoutControlValidation,
      sessionManagementAnalysis,
      autoRefreshDetection,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 80% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Timing Pattern Detection Algorithm - Core Implementation
   * Target: 80% timing pattern detection accuracy
   */
  private async executeTimingPatternDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const timingDetection = await page.evaluate((): TimingPatternDetection => {
      // Advanced timing pattern detection
      const timeoutSelectors = [
        '[data-timeout]', '[data-session]', '.timeout', '.session-warning',
        '#timeout', '#session', '.countdown', '.timer', '[data-timer]'
      ];

      const timeoutElements = document.querySelectorAll(timeoutSelectors.join(', ')).length;

      // Detect session timers
      const sessionTimers = document.querySelectorAll(
        '[data-session-timeout], .session-timer, #session-timer, [data-idle-timeout]'
      ).length;

      // Detect auto-refresh elements
      const autoRefreshElements = document.querySelectorAll(
        'meta[http-equiv="refresh"], [data-auto-refresh], .auto-refresh'
      ).length;

      // Detect countdown timers
      const countdownTimers = document.querySelectorAll(
        '.countdown, .timer, [data-countdown], [data-timer], .clock'
      ).length;

      // Analyze JavaScript for timing patterns
      const timingPatterns: string[] = [];
      const scripts = Array.from(document.querySelectorAll('script'));

      scripts.forEach(script => {
        const scriptContent = script.textContent || '';

        // Look for setTimeout/setInterval patterns
        if (scriptContent.includes('setTimeout') || scriptContent.includes('setInterval')) {
          timingPatterns.push('JavaScript timers');
        }

        // Look for session timeout patterns
        if (scriptContent.includes('session') && (scriptContent.includes('timeout') || scriptContent.includes('expire'))) {
          timingPatterns.push('Session timeout');
        }

        // Look for auto-refresh patterns
        if (scriptContent.includes('refresh') || scriptContent.includes('reload')) {
          timingPatterns.push('Auto-refresh');
        }

        // Look for countdown patterns
        if (scriptContent.includes('countdown') || scriptContent.includes('timer')) {
          timingPatterns.push('Countdown timer');
        }
      });

      // Calculate risk level
      const totalTimingElements = timeoutElements + sessionTimers + autoRefreshElements + countdownTimers;
      let riskLevel: TimingPatternDetection['riskLevel'] = 'none';
      let detectionConfidence = 0.8;

      if (totalTimingElements === 0 && timingPatterns.length === 0) {
        riskLevel = 'none';
        detectionConfidence = 0.9;
      } else if (totalTimingElements <= 2 && timingPatterns.length <= 1) {
        riskLevel = 'low';
        detectionConfidence = 0.85;
      } else if (totalTimingElements <= 5 && timingPatterns.length <= 3) {
        riskLevel = 'medium';
        detectionConfidence = 0.8;
      } else {
        riskLevel = 'high';
        detectionConfidence = 0.75;
      }

      return {
        timeoutElements,
        sessionTimers,
        autoRefreshElements,
        countdownTimers,
        timingPatterns: [...new Set(timingPatterns)],
        riskLevel,
        detectionConfidence,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (timingDetection.riskLevel === 'none' || timingDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Timing pattern detection: Low or no timing accessibility risk',
        value: `Risk level: ${timingDetection.riskLevel}, Confidence: ${(timingDetection.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Timing accessibility risk: ${timingDetection.riskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Timing pattern detection: ${timingDetection.riskLevel} risk`,
        value: `Timeouts: ${timingDetection.timeoutElements}, Sessions: ${timingDetection.sessionTimers}, Auto-refresh: ${timingDetection.autoRefreshElements}, Countdowns: ${timingDetection.countdownTimers}`,
        severity: timingDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Add user controls for timing-based content (extend, disable, or adjust)');
    }

    // Report detected timing patterns
    if (timingDetection.timingPatterns.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Detected timing patterns',
        value: timingDetection.timingPatterns.join(', '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Timeout Control Validation Algorithm
   */
  private async validateTimeoutControls(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const timeoutControlValidation = await page.evaluate((): TimeoutControlValidation => {
      // Look for timeout control elements
      const timeoutControls = document.querySelectorAll(
        '[data-timeout-control], .timeout-control, .session-control, .extend-session, .disable-timeout'
      );

      const extendButtons = document.querySelectorAll(
        '.extend, .extend-session, [data-extend], button[onclick*="extend"], button[onclick*="timeout"]'
      );

      const disableButtons = document.querySelectorAll(
        '.disable-timeout, [data-disable-timeout], .no-timeout, [data-no-timeout]'
      );

      const warningElements = document.querySelectorAll(
        '.timeout-warning, .session-warning, [data-timeout-warning], [role="alert"][data-timeout]'
      );

      const hasTimeoutControls = timeoutControls.length > 0;
      const hasExtendOption = extendButtons.length > 0;
      const hasDisableOption = disableButtons.length > 0;
      const hasWarningSystem = warningElements.length > 0;

      const controlTypes: string[] = [];
      if (hasExtendOption) controlTypes.push('extend');
      if (hasDisableOption) controlTypes.push('disable');
      if (hasWarningSystem) controlTypes.push('warning');

      // Check for adequate controls (at least 2 of: extend, disable, warning)
      const adequateControls = controlTypes.length >= 2;

      return {
        hasTimeoutControls,
        hasExtendOption,
        hasDisableOption,
        hasWarningSystem,
        controlTypes,
        adequateControls,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (!timeoutControlValidation.hasTimeoutControls || timeoutControlValidation.adequateControls) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Timeout control validation: Adequate controls available',
        value: `Controls: ${timeoutControlValidation.controlTypes.join(', ') || 'none needed'}`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient timeout controls detected');
      evidence.push({
        type: 'code',
        description: 'Timeout control validation: Inadequate controls',
        value: `Available controls: ${timeoutControlValidation.controlTypes.join(', ') || 'none'} (need at least 2)`,
        severity: 'error',
      });
      recommendations.push('Add timeout controls: extend session, disable timeout, or warning system');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Session Management Analysis Algorithm
   */
  private async analyzeSessionManagement(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const sessionAnalysis = await page.evaluate((): SessionManagementAnalysis => {
      // Check for session timeout indicators
      const sessionElements = document.querySelectorAll(
        '[data-session], .session, #session, [data-idle-timeout], .idle-timeout'
      );

      const hasSessionTimeout = sessionElements.length > 0;

      // Try to extract timeout duration from data attributes or text
      let timeoutDuration = 0;
      sessionElements.forEach(element => {
        const timeoutAttr = element.getAttribute('data-timeout') ||
                           element.getAttribute('data-session-timeout') ||
                           element.getAttribute('data-idle-timeout');

        if (timeoutAttr) {
          const duration = parseInt(timeoutAttr, 10);
          if (!isNaN(duration)) {
            timeoutDuration = Math.max(timeoutDuration, duration);
          }
        }

        // Try to extract from text content
        const text = element.textContent || '';
        const timeMatch = text.match(/(\d+)\s*(minute|min|second|sec|hour|hr)/i);
        if (timeMatch) {
          const value = parseInt(timeMatch[1], 10);
          const unit = timeMatch[2].toLowerCase();
          let seconds = value;

          if (unit.includes('min')) seconds *= 60;
          else if (unit.includes('hour') || unit.includes('hr')) seconds *= 3600;

          timeoutDuration = Math.max(timeoutDuration, seconds);
        }
      });

      // Check for warning before timeout
      const warningElements = document.querySelectorAll(
        '.timeout-warning, .session-warning, [data-timeout-warning], [role="alert"]'
      );
      const hasWarningBeforeTimeout = warningElements.length > 0;

      // Check for data persistence
      const persistenceElements = document.querySelectorAll(
        '[data-persist], .persist, .save-data, [data-autosave], .autosave'
      );
      const hasDataPersistence = persistenceElements.length > 0;

      // Check for auto-save functionality
      const autoSaveElements = document.querySelectorAll(
        '.autosave, [data-autosave], .auto-save, [data-auto-save]'
      );
      const hasAutoSave = autoSaveElements.length > 0;

      // Determine session risk level
      let sessionRiskLevel: SessionManagementAnalysis['sessionRiskLevel'] = 'low';

      if (hasSessionTimeout) {
        if (timeoutDuration > 0 && timeoutDuration < 1200) { // Less than 20 minutes
          sessionRiskLevel = 'high';
        } else if (!hasWarningBeforeTimeout && !hasDataPersistence) {
          sessionRiskLevel = 'medium';
        }
      }

      return {
        hasSessionTimeout,
        timeoutDuration,
        hasWarningBeforeTimeout,
        hasDataPersistence,
        hasAutoSave,
        sessionRiskLevel,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (!sessionAnalysis.hasSessionTimeout || sessionAnalysis.sessionRiskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Session management analysis: Low risk session management',
        value: `Risk level: ${sessionAnalysis.sessionRiskLevel}, Timeout: ${sessionAnalysis.timeoutDuration}s`,
        severity: 'info',
      });
    } else {
      issues.push(`Session management risk: ${sessionAnalysis.sessionRiskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Session management analysis: ${sessionAnalysis.sessionRiskLevel} risk`,
        value: `Timeout: ${sessionAnalysis.timeoutDuration}s, Warning: ${sessionAnalysis.hasWarningBeforeTimeout}, Persistence: ${sessionAnalysis.hasDataPersistence}, AutoSave: ${sessionAnalysis.hasAutoSave}`,
        severity: sessionAnalysis.sessionRiskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Improve session management: add warnings, data persistence, or extend timeout duration');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Auto-Refresh Detection Algorithm
   */
  private async detectAutoRefresh(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const autoRefreshAnalysis = await page.$$eval('meta[http-equiv="refresh"], [data-auto-refresh]', (elements) => {
      return elements.map((element, index) => {
        const isMetaRefresh = element.tagName.toLowerCase() === 'meta';
        let refreshInterval = 0;
        let hasUserControl = false;

        if (isMetaRefresh) {
          const content = element.getAttribute('content') || '';
          const intervalMatch = content.match(/^(\d+)/);
          if (intervalMatch) {
            refreshInterval = parseInt(intervalMatch[1], 10);
          }
        } else {
          const intervalAttr = element.getAttribute('data-refresh-interval') ||
                              element.getAttribute('data-auto-refresh');
          if (intervalAttr) {
            refreshInterval = parseInt(intervalAttr, 10);
          }
        }

        // Check for user controls
        const parentContainer = element.closest('div, section, article') || document.body;
        const hasStopButton = parentContainer.querySelector('.stop-refresh, .pause-refresh, [data-stop-refresh]') !== null;
        const hasDisableButton = parentContainer.querySelector('.disable-refresh, [data-disable-refresh]') !== null;
        const hasControlPanel = parentContainer.querySelector('.refresh-controls, .auto-refresh-controls') !== null;

        hasUserControl = hasStopButton || hasDisableButton || hasControlPanel;

        return {
          index,
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          isMetaRefresh,
          refreshInterval,
          hasUserControl,
          isProblematic: refreshInterval > 0 && refreshInterval < 72000 && !hasUserControl, // Less than 20 hours without control
        };
      });
    });

    const totalChecks = autoRefreshAnalysis.length;
    let passedChecks = 0;

    autoRefreshAnalysis.forEach((refresh, index) => {
      if (!refresh.isProblematic) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Auto-refresh element ${index + 1} is properly controlled`,
          value: `${refresh.selector} - interval: ${refresh.refreshInterval}s, user control: ${refresh.hasUserControl}`,
          severity: 'info',
        });
      } else {
        issues.push(`Auto-refresh element ${index + 1} lacks user control`);
        evidence.push({
          type: 'code',
          description: `Auto-refresh element ${index + 1} needs user control`,
          value: `${refresh.selector} - interval: ${refresh.refreshInterval}s, no user control`,
          severity: 'error',
        });
        recommendations.push(`Add user control for auto-refresh element ${index + 1} (stop, pause, or disable)`);
      }
    });

    // If no auto-refresh elements found, that's good
    if (totalChecks === 0) {
      return {
        totalChecks: 1,
        passedChecks: 1,
        evidence: [{
          type: 'text',
          description: 'Auto-refresh detection: No problematic auto-refresh detected',
          value: 'No auto-refresh elements found',
          severity: 'info',
        }],
        issues: [],
        recommendations: [],
      };
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

}

      // Check for session timeout warnings
      const sessionElements = document.querySelectorAll(
        '[data-timeout], [data-session], .timeout, .session-warning, #timeout, #session'
      );
      
      sessionElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'session_timeout',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Session timeout element detected',
        });
      });

      // Check for auto-refresh meta tags
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach((meta, index) => {
        const content = meta.getAttribute('content') || '';
        const timeMatch = content.match(/^(\d+)/);
        const timeValue = timeMatch ? timeMatch[1] : '';
        
        timingElements.push({
          type: 'meta_refresh',
          element: 'meta',
          selector: `meta[http-equiv="refresh"]:nth-of-type(${index + 1})`,
          hasControls: false,
          timeValue,
          description: `Auto-refresh meta tag with ${timeValue} second delay`,
        });
      });

      // Check for JavaScript timers (common patterns)
      const scripts = document.querySelectorAll('script');
      let hasTimerCode = false;
      
      scripts.forEach((script) => {
        const scriptContent = script.textContent || '';
        if (scriptContent.includes('setTimeout') || 
            scriptContent.includes('setInterval') ||
            scriptContent.includes('location.reload') ||
            scriptContent.includes('window.location.href')) {
          hasTimerCode = true;
        }
      });

      if (hasTimerCode) {
        timingElements.push({
          type: 'javascript_timer',
          element: 'script',
          selector: 'script',
          hasControls: false,
          description: 'JavaScript timer or redirect code detected',
        });
      }

      // Check for countdown timers
      const countdownElements = document.querySelectorAll(
        '.countdown, .timer, [data-countdown], [data-timer], #countdown, #timer'
      );
      
      countdownElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'countdown_timer',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Countdown timer element detected',
        });
      });

      // Check for form timeout warnings
      const formTimeouts = document.querySelectorAll('form [data-timeout], form .timeout');
      formTimeouts.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, a, [role="button"]') !== null;
        timingElements.push({
          type: 'form_timeout',
          element: element.tagName.toLowerCase(),
          selector: `form ${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Form timeout warning detected',
        });
      });

      return {
        timingElements,
        totalElements: timingElements.length,
        elementsWithControls: timingElements.filter(el => el.hasControls).length,
      };
    });

    let score = 100;
    const elementCount = timingAnalysis.totalElements;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      const elementsWithoutControls = elementCount - timingAnalysis.elementsWithControls;
      
      if (elementsWithoutControls > 0) {
        score = Math.max(0, 100 - (elementsWithoutControls * 25)); // Deduct 25 points per element without controls
        issues.push(`${elementsWithoutControls} timing elements found without user controls`);
        
        timingAnalysis.timingElements
          .filter(el => !el.hasControls)
          .forEach((element) => {
            evidence.push({
              type: 'code',
              description: `Timing element without user controls: ${element.description}`,
              value: element.timeValue ? 
                `Time limit: ${element.timeValue} seconds` : 
                'Timing element detected',
              selector: element.selector,
              elementCount: 1,
              affectedSelectors: [element.selector],
              severity: 'error',
              fixExample: {
                before: this.getBeforeExample(element.type),
                after: this.getAfterExample(element.type),
                description: this.getFixDescription(element.type),
                codeExample: this.getCodeExample(element.type),
                resources: [
                  'https://www.w3.org/WAI/WCAG21/Understanding/timing-adjustable.html',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G133',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G180'
                ]
              },
              metadata: {
                scanDuration,
                elementsAnalyzed: 1,
                checkSpecificData: {
                  timingType: element.type,
                  hasControls: element.hasControls,
                  timeValue: element.timeValue || 'unknown',
                },
              },
            });
          });
        
        recommendations.push('Provide user controls to turn off, adjust, or extend time limits');
        recommendations.push('Allow users to extend time limits by at least 10 times the default');
        recommendations.push('Warn users before time expires and provide at least 20 seconds to extend');
        recommendations.push('Consider removing automatic time limits where possible');
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return '<meta http-equiv="refresh" content="30; url=next-page.html">';
      case 'session_timeout':
        return '<div class="timeout">Session will expire in 5 minutes</div>';
      case 'countdown_timer':
        return '<div class="countdown">Time remaining: 00:05:00</div>';
      case 'form_timeout':
        return '<div class="timeout">Form will timeout in 10 minutes</div>';
      default:
        return 'Timing element without controls';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return '<!-- Remove auto-refresh or provide user control -->\n<button onclick="refreshPage()">Refresh Page</button>';
      case 'session_timeout':
        return '<div class="timeout">Session will expire in 5 minutes\n  <button onclick="extendSession()">Extend Session</button>\n</div>';
      case 'countdown_timer':
        return '<div class="countdown">Time remaining: 00:05:00\n  <button onclick="pauseTimer()">Pause</button>\n  <button onclick="extendTimer()">Extend Time</button>\n</div>';
      case 'form_timeout':
        return '<div class="timeout">Form will timeout in 10 minutes\n  <button onclick="extendTimeout()">Extend Time</button>\n</div>';
      default:
        return 'Timing element with user controls';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return 'Remove automatic refresh or provide user control to refresh';
      case 'session_timeout':
        return 'Add controls to extend or disable session timeout';
      case 'countdown_timer':
        return 'Provide controls to pause, stop, or extend countdown timers';
      case 'form_timeout':
        return 'Add controls to extend form timeout periods';
      default:
        return 'Provide user controls for timing elements';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'meta_refresh':
        return `
<!-- Before: Automatic refresh -->
<meta http-equiv="refresh" content="30; url=next-page.html">

<!-- After: User-controlled refresh -->
<script>
function refreshPage() {
  if (confirm('Refresh the page?')) {
    window.location.reload();
  }
}
</script>
<button onclick="refreshPage()">Refresh Page</button>
        `;
      case 'session_timeout':
        return `
<!-- Before: No user control -->
<div class="timeout">Session expires in 5 minutes</div>

<!-- After: With user controls -->
<div class="timeout">
  Session expires in <span id="timer">5:00</span>
  <button onclick="extendSession()">Extend Session</button>
  <button onclick="disableTimeout()">Disable Timeout</button>
</div>
        `;
      case 'countdown_timer':
        return `
<!-- Before: No user control -->
<div class="countdown">Time remaining: 00:05:00</div>

<!-- After: With user controls -->
<div class="countdown">
  Time remaining: <span id="countdown">00:05:00</span>
  <button onclick="pauseTimer()">Pause</button>
  <button onclick="extendTimer()">Extend Time</button>
  <button onclick="stopTimer()">Stop Timer</button>
</div>
        `;
      case 'form_timeout':
        return `
<!-- Before: No user control -->
<div class="timeout">Form will timeout in 10 minutes</div>

<!-- After: With user controls -->
<div class="timeout">
  Form will timeout in <span id="form-timer">10:00</span>
  <button onclick="extendTimeout()">Extend Time</button>
  <button onclick="saveAndContinue()">Save & Continue Later</button>
</div>
        `;
      default:
        return 'Provide appropriate user controls for timing elements';
    }
  }
}
