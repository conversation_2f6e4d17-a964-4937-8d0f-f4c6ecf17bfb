/**
 * WCAG-029: Page Titled Check
 * Success Criterion: 2.4.2 Page Titled (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface PageTitleAnalysis {
  hasTitle: boolean;
  titleText: string;
  titleLength: number;
  isEmpty: boolean;
  isGeneric: boolean;
  isDescriptive: boolean;
  hasPageContext: boolean;
  hasSiteContext: boolean;
  titleElement?: {
    tagName: string;
    innerHTML: string;
    textContent: string;
  };
  metaTitle?: string;
  ogTitle?: string;
  duplicateElements: number;
}

export interface PageTitledConfig extends EnhancedCheckConfig {
  enableContentQualityAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableCMSDetection?: boolean;
  checkTitleUniqueness?: boolean;
  checkDescriptiveness?: boolean;
}

export class PageTitledCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Generic/non-descriptive title patterns
  private readonly genericPatterns = [
    /^untitled$/i,
    /^new page$/i,
    /^page$/i,
    /^document$/i,
    /^welcome$/i,
    /^home$/i,
    /^index$/i,
    /^default$/i,
    /^test$/i,
    /^sample$/i,
    /^example$/i,
    /^lorem ipsum$/i,
    /^placeholder$/i,
    /^title$/i,
    /^website$/i,
    /^site$/i,
  ];

  async performCheck(config: PageTitledConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: PageTitledConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableContentQualityAnalysis: true,
      enableSemanticValidation: true,
      enableCMSDetection: true,
      checkTitleUniqueness: true,
      checkDescriptiveness: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-029',
      'Page Titled',
      'operable',
      0.0815,
      'A',
      enhancedConfig,
      this.executePageTitledCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with page title analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-013',
        ruleName: 'Page Titled',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'page-title-analysis',
          titleAnalysis: true,
          metadataAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 10, // Page titles are typically single elements
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'page-title-analysis',
        confidence: 1.0,
        additionalData: {
          checkType: 'content-validation',
          automationLevel: 'full',
        },
      },
    };
  }

  private async executePageTitledCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze page title
    const titleAnalysis = await page.evaluate((): PageTitleAnalysis => {
      const titleElements = document.querySelectorAll('title');
      const titleElement = titleElements[0];
      
      const titleText = document.title || '';
      const titleLength = titleText.trim().length;
      const isEmpty = titleLength === 0;
      
      // Check for generic patterns
      const isGeneric = titleText.trim().toLowerCase().match(
        /^(untitled|new page|page|document|welcome|home|index|default|test|sample|example|lorem ipsum|placeholder|title|website|site)$/
      ) !== null;

      // Check if title is descriptive (not generic, not empty, reasonable length)
      const isDescriptive = !isEmpty && !isGeneric && titleLength >= 3 && titleLength <= 120;

      // Check for page context (specific page information)
      const hasPageContext = titleText.includes(' - ') || 
                            titleText.includes(' | ') || 
                            titleText.includes(' :: ') ||
                            titleText.includes(' › ') ||
                            titleLength > 10;

      // Check for site context (site name or brand)
      const hasCommonSeparators = titleText.includes(' - ') || 
                                 titleText.includes(' | ') || 
                                 titleText.includes(' :: ');
      const hasSiteContext = hasCommonSeparators || titleText.split(' ').length > 2;

      // Get meta and og titles for comparison
      const metaTitleElement = document.querySelector('meta[name="title"]');
      const ogTitleElement = document.querySelector('meta[property="og:title"]');
      
      return {
        hasTitle: !!titleElement,
        titleText,
        titleLength,
        isEmpty,
        isGeneric,
        isDescriptive,
        hasPageContext,
        hasSiteContext,
        titleElement: titleElement ? {
          tagName: titleElement.tagName.toLowerCase(),
          innerHTML: titleElement.innerHTML,
          textContent: titleElement.textContent || '',
        } : undefined,
        metaTitle: metaTitleElement?.getAttribute('content') || undefined,
        ogTitle: ogTitleElement?.getAttribute('content') || undefined,
        duplicateElements: titleElements.length,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = 1; // Always 1 title element expected

    // Check if title element exists
    if (!titleAnalysis.hasTitle) {
      score = 0;
      issues.push('Page missing title element');
      
      evidence.push({
        type: 'error',
        description: 'Page missing title element',
        value: 'No <title> element found in document head',
        selector: 'head',
        elementCount: 0,
        affectedSelectors: ['title'],
        severity: 'error',
        fixExample: {
          before: `<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>`,
          after: `<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Descriptive Page Title - Site Name</title>
</head>`,
          description: 'Add descriptive title element to document head',
          codeExample: `
<!-- Good title examples -->
<title>Contact Us - Acme Corporation</title>
<title>Product Details: Widget Pro - Online Store</title>
<title>About Our Services | Professional Consulting</title>

<!-- Avoid generic titles -->
<title>Home</title>
<title>Page</title>
<title>Untitled Document</title>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/page-titled.html',
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/title',
            'https://webaim.org/techniques/pagetitle/'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            missingTitle: true,
            hasMetaTitle: !!titleAnalysis.metaTitle,
            hasOgTitle: !!titleAnalysis.ogTitle,
          },
        },
      });
      
      recommendations.push('Add a <title> element to the document head');
      recommendations.push('Use descriptive titles that identify the page content and context');
      
    } else if (titleAnalysis.isEmpty) {
      score = 0;
      issues.push('Page title is empty');
      
      evidence.push({
        type: 'error',
        description: 'Empty page title',
        value: '<title></title>',
        selector: 'title',
        elementCount: 1,
        affectedSelectors: ['title'],
        severity: 'error',
        fixExample: {
          before: '<title></title>',
          after: '<title>Descriptive Page Title - Site Name</title>',
          description: 'Provide meaningful content for the title element',
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/page-titled.html'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            titleText: titleAnalysis.titleText,
            titleLength: titleAnalysis.titleLength,
            isEmpty: true,
          },
        },
      });
      
      recommendations.push('Provide meaningful content for the title element');
      
    } else if (titleAnalysis.isGeneric) {
      score = 20; // Partial failure for generic titles
      issues.push(`Page title is generic: "${titleAnalysis.titleText}"`);
      
      evidence.push({
        type: 'error',
        description: 'Generic page title',
        value: `<title>${titleAnalysis.titleText}</title>`,
        selector: 'title',
        elementCount: 1,
        affectedSelectors: ['title'],
        severity: 'error',
        fixExample: {
          before: `<title>${titleAnalysis.titleText}</title>`,
          after: '<title>Specific Page Content - Site Name</title>',
          description: 'Replace generic title with specific, descriptive content',
          codeExample: `
<!-- Instead of generic titles like: -->
<title>Home</title>
<title>Welcome</title>
<title>Page</title>

<!-- Use descriptive titles like: -->
<title>Home - Acme Corporation</title>
<title>Welcome to Our Services | Professional Consulting</title>
<title>Product Catalog - Online Electronics Store</title>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/page-titled.html',
            'https://webaim.org/techniques/pagetitle/'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            titleText: titleAnalysis.titleText,
            titleLength: titleAnalysis.titleLength,
            isGeneric: true,
            genericPattern: this.getMatchingGenericPattern(titleAnalysis.titleText),
          },
        },
      });
      
      recommendations.push('Use specific, descriptive titles instead of generic terms');
      
    } else if (!titleAnalysis.isDescriptive) {
      score = 60; // Partial score for non-descriptive titles
      issues.push('Page title may not be sufficiently descriptive');
      
      const issue = titleAnalysis.titleLength < 3 ? 'too short' : 
                   titleAnalysis.titleLength > 120 ? 'too long' : 'not descriptive enough';
      
      evidence.push({
        type: 'warning',
        description: `Page title ${issue}`,
        value: `<title>${titleAnalysis.titleText}</title>`,
        selector: 'title',
        elementCount: 1,
        affectedSelectors: ['title'],
        severity: 'warning',
        fixExample: {
          before: `<title>${titleAnalysis.titleText}</title>`,
          after: '<title>Clear, Descriptive Page Title - Site Name</title>',
          description: 'Improve title to be more descriptive and appropriately sized',
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/page-titled.html'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            titleText: titleAnalysis.titleText,
            titleLength: titleAnalysis.titleLength,
            issue,
            hasPageContext: titleAnalysis.hasPageContext,
            hasSiteContext: titleAnalysis.hasSiteContext,
          },
        },
      });
      
      if (titleAnalysis.titleLength < 3) {
        recommendations.push('Use longer, more descriptive titles (at least 3 characters)');
      } else if (titleAnalysis.titleLength > 120) {
        recommendations.push('Keep titles concise (under 120 characters for better display)');
      } else {
        recommendations.push('Make titles more descriptive of the page content');
      }
      
    } else {
      // Good title - add positive evidence
      evidence.push({
        type: 'info',
        description: 'Page has descriptive title',
        value: `<title>${titleAnalysis.titleText}</title>`,
        selector: 'title',
        elementCount: 1,
        affectedSelectors: ['title'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            titleText: titleAnalysis.titleText,
            titleLength: titleAnalysis.titleLength,
            isDescriptive: true,
            hasPageContext: titleAnalysis.hasPageContext,
            hasSiteContext: titleAnalysis.hasSiteContext,
          },
        },
      });
    }

    // Check for multiple title elements
    if (titleAnalysis.duplicateElements > 1) {
      score = Math.max(score - 10, 0);
      issues.push(`Found ${titleAnalysis.duplicateElements} title elements (should be exactly 1)`);
      
      evidence.push({
        type: 'warning',
        description: 'Multiple title elements found',
        value: `${titleAnalysis.duplicateElements} title elements detected`,
        selector: 'title',
        elementCount: titleAnalysis.duplicateElements,
        affectedSelectors: ['title'],
        severity: 'warning',
        fixExample: {
          before: `<head>
  <title>First Title</title>
  <title>Second Title</title>
</head>`,
          after: `<head>
  <title>Single Descriptive Title</title>
</head>`,
          description: 'Use exactly one title element per page',
          resources: [
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/title'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: titleAnalysis.duplicateElements,
          checkSpecificData: {
            duplicateCount: titleAnalysis.duplicateElements,
          },
        },
      });
      
      recommendations.push('Use exactly one title element per page');
    }

    // General recommendations
    if (score === 100) {
      recommendations.push('Continue using descriptive, unique titles for each page');
      recommendations.push('Consider including site name or brand in title');
    } else {
      recommendations.push('Use descriptive titles that identify the page topic and purpose');
      recommendations.push('Include both page-specific and site context in titles');
      recommendations.push('Keep titles between 10-60 characters for optimal display');
      recommendations.push('Test titles with screen readers and browser tabs');
    }

    if (!titleAnalysis.hasPageContext) {
      recommendations.push('Include specific page information in the title');
    }

    if (!titleAnalysis.hasSiteContext) {
      recommendations.push('Consider including site name or brand in the title');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getMatchingGenericPattern(text: string): string | undefined {
    const normalizedText = text.trim().toLowerCase();
    const matchingPattern = this.genericPatterns.find(pattern => pattern.test(normalizedText));
    return matchingPattern?.source;
  }
}
